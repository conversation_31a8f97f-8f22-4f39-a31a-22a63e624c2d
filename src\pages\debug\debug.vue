<template>
  <view class="debug-page">
    <!-- 设备状态 -->
    <view class="section">
      <view class="status-card">
        <view class="status-content">
          <view class="status-icon-wrapper">
            <view class="status-icon" :class="statusIconClass" />
          </view>
          <view class="status-text-wrapper">
            <view class="status-title">设备状态</view>
            <view v-if="isConnected" class="disconnect-btn" @click="handleDisconnect">
              断开连接
            </view>
            <view v-else class="status-subtitle">未连接</view>
          </view>
        </view>
        <view class="device-details">
          <view class="detail-item">
            <text class="detail-label">名称:</text>
            <text class="detail-value">{{ deviceInfo.name || "--" }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">ID:</text>
            <text class="detail-value">{{ deviceInfo.id || "--" }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 调试工具 -->
    <view class="section">
      <view class="tool-grid">
        <view class="tool-item" @click="scanDevice">
          <view class="tool-icon-wrapper tool-item-scan">
            <text class="tool-icon">📡</text>
          </view>
          <text class="tool-name">扫描设备</text>
        </view>
        <view class="tool-item" @click="testConnection">
          <view class="tool-icon-wrapper tool-item-test">
            <text class="tool-icon">✈️</text>
          </view>
          <text class="tool-name">连接测试</text>
        </view>
        <view class="tool-item" @click="sendCommand">
          <view class="tool-icon-wrapper tool-item-send">
            <text class="tool-icon">📤</text>
          </view>
          <text class="tool-name">发送指令</text>
        </view>
        <view class="tool-item" @click="getNetwork">
          <view class="tool-icon-wrapper tool-item-read">
            <text class="tool-icon">😈</text>
          </view>
          <text class="tool-name">读取网络</text>
        </view>
      </view>
    </view>

    <!-- 日志输出 -->
    <view class="section log-section">
      <view class="log-card">
        <view class="log-header">
          <text class="log-title">调试日志</text>
        </view>
        <scroll-view class="log-content" scroll-y :scroll-top="scrollTop">
          <view v-if="debugLogs.length === 0" class="log-empty">
            <text class="empty-text">暂无日志</text>
          </view>
          <view v-else class="log-list">
            <view class="log-item" v-for="(log, index) in debugLogs" :key="index" :class="log.type">
              <text class="log-message">{{ log.message }}</text>
              <text class="log-time">{{ log.time }}</text>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>

    <!-- 设备选择弹窗 (iOS 风格) -->
    <view class="modal-overlay" v-if="showDeviceModal" @click="showDeviceModal = false">
      <view class="modal-container" @click.stop>
        <view class="modal-header">
          <text class="modal-title">选择设备</text>
        </view>
        <scroll-view class="device-list" scroll-y>
          <view
            class="device-item"
            v-for="device in foundDevices"
            :key="device.deviceId"
            @click="selectAndConnectDevice(device)"
          >
            <view class="device-icon-wrapper">
              <text class="device-icon">📱</text>
            </view>
            <view class="device-info">
              <text class="device-name">{{ device.name || "Unnamed Device" }}</text>
              <text class="device-id">{{ device.deviceId }}</text>
            </view>
          </view>
        </scroll-view>
        <view class="modal-footer">
          <button class="cancel-button" @click="showDeviceModal = false">取消</button>
        </view>
      </view>
    </view>

    <!-- 悬浮 TabBar -->
    <!-- <CustomTabBar :current="1" /> -->
  </view>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { useBLE } from "../../composables/useBLE";
// import CustomTabBar from "@/components/CustomTabBar.vue";

const scrollTop = ref(0);

const {
  isConnected,
  deviceInfo,
  foundDevices,
  debugLogs,
  showDeviceModal,
  scanDevice,
  selectAndConnectDevice,
  testConnection,
  sendCommand,
  addLog,
  disconnectDevice,
  readBLECharacteristicValue,
} = useBLE();

const statusIconClass = ref("");

const getNetwork = () => {
  readBLECharacteristicValue("0000ffff-1004-0001-2802-018833401286");
};

// 监听连接状态
watch(
  isConnected,
  newVal => {
    statusIconClass.value = newVal ? "status-connected" : "status-disconnected";
  },
  { immediate: true }
);

// 监听日志
watch(
  debugLogs,
  newLogs => {
    if (!isConnected.value || newLogs.length === 0) return;

    const lastLog = newLogs[newLogs.length - 1];
    let blinkClass = "";

    switch (lastLog.type) {
      case "success":
        blinkClass = "status-success-blink";
        break;
      case "warning":
        blinkClass = "status-warning-blink";
        break;
      case "error":
        blinkClass = "status-error-blink";
        break;
      default:
        // 普通日志不闪烁
        return;
    }

    statusIconClass.value = blinkClass;

    // 1.5秒后恢复为常亮状态
    setTimeout(() => {
      // 检查状态，避免在断开连接后又变绿
      if (isConnected.value) {
        statusIconClass.value = "status-connected";
      }
    }, 1500);
  },
  { deep: true } // deep watch 确保能监听到数组变化
);

const handleDisconnect = () => {
  uni.showModal({
    title: "提示",
    content: "是否确定断开连接？",
    success: res => {
      if (res.confirm) {
        disconnectDevice();
      }
    },
  });
};

// 自动滚动到底部
watch(
  debugLogs,
  () => {
    setTimeout(() => {
      scrollTop.value = debugLogs.value.length * 100;
    }, 100);
  },
  {
    deep: true,
  }
);
</script>

<style lang="scss" scoped>
@use "sass:color";
.debug-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f0f0f5;
  padding: 0 32rpx;
  box-sizing: border-box;
}

.section {
  margin-bottom: 32rpx;
  flex-shrink: 0;
}

.log-section {
  flex: 1;
  min-height: 0; // 关键：允许flex子元素收缩
  display: flex;
  flex-direction: column;
}

/* 设备状态卡片 */
.status-card {
  background-color: $uni-bg-color;
  border-radius: 32rpx;
  padding: 32rpx;
  margin-top: 32rpx;
  transition: all 0.3s ease;
  border: 2rpx solid transparent;

  .status-content {
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;
    width: 100%;
  }

  .status-icon-wrapper {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    background-color: $uni-bg-color-grey;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 24rpx;
    box-shadow: inset 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
  }

  .status-icon {
    width: 32rpx;
    height: 32rpx;
    border-radius: 50%;
    background-color: $uni-text-color-placeholder; // 默认或断开连接
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;

    &::before {
      content: "";
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 100%;
      height: 100%;
      border-radius: 50%;
      box-shadow: 0 0 12rpx 4rpx transparent;
      transition: box-shadow 0.3s ease;
    }

    &.status-connected {
      background-color: $uni-color-success;
      box-shadow: 0 0 16rpx rgba($uni-color-success, 0.5);
    }

    &.status-disconnected {
      background-color: $uni-text-color-placeholder;
      box-shadow: none;
    }

    &.status-success-blink {
      animation: blink-success 1.5s ease-in-out;
    }

    &.status-warning-blink {
      animation: blink-warning 1.5s ease-in-out;
    }

    &.status-error-blink {
      animation: blink-error 1.5s ease-in-out;
    }
  }

  .status-text-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex: 1;
    .status-title {
      font-size: 32rpx;
      font-weight: 600;
      color: $uni-text-color;
      display: block;
    }
    .status-subtitle {
      font-size: 28rpx;
      color: $uni-text-color-grey;
    }

    .disconnect-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 8rpx 24rpx;
      margin-top: 8rpx;
      border-radius: 9999rpx; // iOS 胶囊风格
      font-size: 24rpx;
      font-weight: 600;
      color: $uni-text-color-inverse;
      background-color: $uni-color-error;
      border: none;
      transition: background-color 0.2s ease;
      line-height: 1; // 确保文字垂直居中

      // 移除 uni-app 按钮的默认边框
      &::after {
        border: none;
      }

      &:active {
        background-color: color.adjust($uni-color-error, $lightness: -10%);
      }
    }
  }

  .device-details {
    border-top: 2rpx solid $uni-border-color;
    padding-top: 24rpx;
    .detail-item {
      display: flex;
      justify-content: space-between;
      font-size: 26rpx;
      margin-bottom: 12rpx;
      .detail-label {
        color: $uni-text-color-grey;
      }
      .detail-value {
        color: $uni-text-color;
        font-weight: 500;
      }
    }
  }
}

/* 调试工具 */
.tool-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24rpx;
}

.tool-item {
  background-color: $uni-bg-color;
  border-radius: 32rpx;
  padding: 32rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s ease;
  &:active {
    transform: scale(0.95);
  }
}

.tool-icon-wrapper {
  width: 100rpx;
  height: 100rpx;
  border-radius: 28rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 16rpx;
  .tool-icon {
    font-size: 48rpx;
  }
}

.tool-item-scan {
  background-color: $uni-color-generic-gradient-1;
}
.tool-item-test {
  background-color: $uni-color-generic-gradient-3;
}
.tool-item-send {
  background-color: $uni-color-generic-gradient-5;
}

.tool-name {
  font-size: 26rpx;
  font-weight: 500;
  color: $uni-text-color;
}

/* 日志卡片 */
.log-card {
  background-color: $uni-bg-color;
  border-radius: 32rpx;
  padding: 32rpx;
}

.log-header {
  margin-bottom: 24rpx;
  .log-title {
    font-size: 32rpx;
    font-weight: 600;
    color: $uni-text-color;
  }
}

.log-content {
  height: calc(100vh - 780rpx);
  width: calc(100% - 20rpx);
  background-color: #1e1e1e;
  border-radius: 24rpx;
  padding: 12rpx;
  color: #fff;
  min-height: 0; /* 确保在flex容器中可以正确缩小 */
}

.log-empty {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  .empty-text {
    color: $uni-text-color-grey;
    font-size: 28rpx;
  }
}

.log-list {
  .log-item {
    padding: 12rpx;
    border-radius: 12rpx;
    margin-bottom: 12rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-family: "Courier New", monospace;
    text-align: left;

    &.success {
      background-color: rgba($uni-color-success, 0.1);
      .log-message {
        color: $uni-color-success;
      }
    }
    &.warning {
      background-color: rgba($uni-color-warning, 0.1);
      .log-message {
        color: $uni-color-warning;
      }
    }
    &.error {
      background-color: rgba($uni-color-error, 0.1);
      .log-message {
        color: $uni-color-error;
      }
    }

    .log-message {
      font-size: 24rpx;
      word-break: break-all;
    }
    .log-time {
      font-size: 22rpx;
      color: $uni-text-color-grey;
      flex-shrink: 0;
      margin-left: 16rpx;
    }
  }
}

/* 设备选择弹窗 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: flex-end;
  z-index: 1000;
}

.modal-container {
  background-color: $uni-bg-color-grey;
  width: 100%;
  border-top-left-radius: 40rpx;
  border-top-right-radius: 40rpx;
  padding: 16rpx 32rpx 40rpx;
  box-sizing: border-box;
}

.modal-header {
  text-align: center;
  padding: 24rpx 0;
  .modal-title {
    font-size: 34rpx;
    font-weight: 600;
  }
}

.device-list {
  max-height: 50vh;
  background-color: $uni-bg-color;
  border-radius: 24rpx;
  margin-bottom: 24rpx;
}

.device-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border-bottom: 2rpx solid $uni-bg-color-grey;
  &:last-child {
    border-bottom: none;
  }
  &:active {
    background-color: $uni-bg-color-hover;
  }
}

.device-icon-wrapper {
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx;
  background-color: $uni-bg-color-grey;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 24rpx;
  .device-icon {
    font-size: 40rpx;
  }
}

.device-info {
  .device-name {
    font-size: 30rpx;
    color: $uni-text-color;
    font-weight: 500;
  }
  .device-id {
    font-size: 24rpx;
    color: $uni-text-color-grey;
  }
}

.modal-footer {
  .cancel-button {
    background-color: $uni-bg-color;
    color: $uni-text-color;
    font-size: 32rpx;
    font-weight: 500;
    border-radius: 24rpx;
    height: 100rpx;
    line-height: 100rpx;
    text-align: center;
    &:active {
      background-color: $uni-bg-color-hover;
    }
  }
}

@keyframes blink-success {
  0%,
  100% {
    background-color: $uni-color-success;
    box-shadow: 0 0 16rpx rgba($uni-color-success, 0.5);
  }
  50% {
    background-color: color.adjust($uni-color-success, $lightness: 15%);
    box-shadow: 0 0 24rpx rgba($uni-color-success, 0.8);
  }
}

@keyframes blink-warning {
  0%,
  100% {
    background-color: $uni-color-warning;
    box-shadow: 0 0 16rpx rgba($uni-color-warning, 0.5);
  }
  50% {
    background-color: color.adjust($uni-color-warning, $lightness: 15%);
    box-shadow: 0 0 24rpx rgba($uni-color-warning, 0.8);
  }
}

@keyframes blink-error {
  0%,
  100% {
    background-color: $uni-color-error;
    box-shadow: 0 0 16rpx rgba($uni-color-error, 0.5);
  }
  50% {
    background-color: color.adjust($uni-color-error, $lightness: 15%);
    box-shadow: 0 0 24rpx rgba($uni-color-error, 0.8);
  }
}
</style>
