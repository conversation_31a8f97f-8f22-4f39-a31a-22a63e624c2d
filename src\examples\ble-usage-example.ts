/**
 * 蓝牙读写方法使用示例
 * 
 * 本文件展示了如何使用 useBLE composable 中新增的 
 * writeBLECharacteristic 和 readBLECharacteristic 方法
 */

import { useBLE } from '@/composables/useBLE'
import { DataType } from '@/utils/ble-protocol'

// 获取 BLE 实例
const { 
  writeBLECharacteristic, 
  readBLECharacteristic, 
  isConnected,
  addLog 
} = useBLE()

// 示例1：写入字符串数据到指定特征值
export async function writeStringExample() {
  if (!isConnected.value) {
    console.log('设备未连接')
    return
  }

  const characteristicId = "0000ffff-1002-0000-2802-018833401286"
  const data = "Hello BLE Device! This is a test message."
  
  try {
    const success = await writeBLECharacteristic(characteristicId, data, DataType.JSON)
    if (success) {
      console.log('数据写入成功')
    } else {
      console.log('数据写入失败')
    }
  } catch (error) {
    console.error('写入过程中发生错误:', error)
  }
}

// 示例2：写入大量数据（会自动分包）
export async function writeLargeDataExample() {
  if (!isConnected.value) {
    console.log('设备未连接')
    return
  }

  const characteristicId = "0000ffff-1002-0000-2802-018833401286"
  
  // 创建一个较大的 JSON 对象
  const largeData = {
    timestamp: Date.now(),
    data: Array.from({ length: 100 }, (_, i) => ({
      id: i,
      value: Math.random(),
      description: `This is item number ${i} with some additional text to make it larger`
    })),
    metadata: {
      version: "1.0.0",
      author: "BLE Test",
      description: "This is a large data object for testing BLE packet splitting"
    }
  }
  
  const jsonString = JSON.stringify(largeData)
  console.log(`准备发送大数据，大小: ${jsonString.length} 字节`)
  
  try {
    const success = await writeBLECharacteristic(characteristicId, jsonString, DataType.JSON)
    if (success) {
      console.log('大数据写入成功')
    } else {
      console.log('大数据写入失败')
    }
  } catch (error) {
    console.error('写入大数据过程中发生错误:', error)
  }
}

// 示例3：从指定特征值读取数据
export async function readDataExample() {
  if (!isConnected.value) {
    console.log('设备未连接')
    return
  }

  const characteristicId = "0000ffff-1004-0001-2802-018833401286"
  
  try {
    console.log('开始读取数据...')
    const result = await readBLECharacteristic(characteristicId, 15000) // 15秒超时
    
    if (result) {
      console.log(`数据读取成功:`)
      console.log(`- 数据类型: ${DataType[result.type]}`)
      console.log(`- 数据大小: ${result.data.length} 字节`)
      console.log(`- 数据内容: ${result.data.toString()}`)
      
      // 如果是 JSON 数据，尝试解析
      if (result.type === DataType.JSON) {
        try {
          const jsonData = JSON.parse(result.data.toString())
          console.log('解析后的 JSON 数据:', jsonData)
        } catch (parseError) {
          console.log('JSON 解析失败:', parseError)
        }
      }
    } else {
      console.log('数据读取失败或超时')
    }
  } catch (error) {
    console.error('读取数据过程中发生错误:', error)
  }
}

// 示例4：读写组合操作
export async function readWriteComboExample() {
  if (!isConnected.value) {
    console.log('设备未连接')
    return
  }

  const writeCharId = "0000ffff-1002-0000-2802-018833401286"
  const readCharId = "0000ffff-1004-0001-2802-018833401286"
  
  try {
    // 1. 先写入一个命令
    console.log('发送命令...')
    const command = { action: "get_status", timestamp: Date.now() }
    const writeSuccess = await writeBLECharacteristic(
      writeCharId, 
      JSON.stringify(command), 
      DataType.Command
    )
    
    if (!writeSuccess) {
      console.log('命令发送失败')
      return
    }
    
    // 2. 等待一段时间让设备处理
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 3. 读取响应
    console.log('读取响应...')
    const response = await readBLECharacteristic(readCharId, 10000)
    
    if (response) {
      console.log('收到响应:', response.data.toString())
    } else {
      console.log('未收到响应')
    }
    
  } catch (error) {
    console.error('读写组合操作中发生错误:', error)
  }
}

// 示例5：错误处理和重试机制
export async function writeWithRetryExample() {
  if (!isConnected.value) {
    console.log('设备未连接')
    return
  }

  const characteristicId = "0000ffff-1002-0000-2802-018833401286"
  const data = "Test data with retry mechanism"
  const maxRetries = 3
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`尝试写入数据 (第 ${attempt} 次)...`)
      const success = await writeBLECharacteristic(characteristicId, data)
      
      if (success) {
        console.log(`数据写入成功 (第 ${attempt} 次尝试)`)
        return true
      } else {
        console.log(`数据写入失败 (第 ${attempt} 次尝试)`)
        if (attempt < maxRetries) {
          console.log('等待后重试...')
          await new Promise(resolve => setTimeout(resolve, 2000))
        }
      }
    } catch (error) {
      console.error(`第 ${attempt} 次尝试时发生错误:`, error)
      if (attempt < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, 2000))
      }
    }
  }
  
  console.log('所有重试都失败了')
  return false
}

// 导出所有示例函数
export const bleExamples = {
  writeStringExample,
  writeLargeDataExample,
  readDataExample,
  readWriteComboExample,
  writeWithRetryExample
}
