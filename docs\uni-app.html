<!DOCTYPE html>
<html lang="en-US">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <title>uni-app</title>
    <meta name="generator" content="VuePress 1.9.9">
    <link rel="shortcut icon" type="image/x-icon" href="https://qiniu-web-assets.dcloud.net.cn/unidoc/zh/icon.png?v=1556263038788">
    <script src="/js/redirect.js"></script>
    <script src="https://hm.baidu.com/hm.js?fe3b7a223fc08c795f0f4b6350703e6f"></script>
    <script src="/miku-delivery-1.2.1.js"></script>
    <script src="/js/miku.js?1718983858042&amp;v=1718983858042&amp;version=1718983858042"></script>
    <link rel="shortcut icon" type="image/x-icon" href="https://vkceyugu.cdn.bspapp.com/VKCEYUGU-a90b5f95-90ba-4d30-a6a7-cd4d057327db/d23e842c-58fc-4574-998d-17fdc7811cc3.png?v=1556263038788">
    <script src="/js/redirect.js?1718983858042&amp;v=1718983858042&amp;version=1718983858042"></script>
    <script src="https://hm.baidu.com/hm.js?335faa6b53d8671e088767b0b95a706e"></script>
    <meta name="description" content="uni-app,uniCloud,serverless,uni.openBluetoothAdapter(OBJECT),Error,uni.startBluetoothDevicesDiscovery(OBJECT),Error,uni.onBluetoothDeviceFound(CALLBAC">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimum-scale=1.0, maximum-scale=1.0">
    <meta name="keywords" content="serverless,云开发,数字天堂,前端开发,web开发,小程序开发,跨平台,跨平台开发,跨端开发,混合开发,app开发,多端开发,开发工具,HTML5,vue,react,native,rn,flutter,weex,cordova,微信小程序,阿里小程序,支付宝小程序,百度小程序,头条小程序,抖音小程序,QQ小程序,快应用,流应用,云函数">
    
    <link rel="preload" href="/2024-06-21_23-30-58/assets/css/0.styles.3a3b1b19.css" as="style"><link rel="preload" href="/2024-06-21_23-30-58/assets/js/app.deebca7c.js" as="script"><link rel="preload" href="/2024-06-21_23-30-58/assets/js/vendors~layout-Layout.44850949.js" as="script"><link rel="preload" href="/2024-06-21_23-30-58/assets/js/497.bff5d4d0.js" as="script"><link rel="preload" href="/2024-06-21_23-30-58/assets/js/docs/api/system/bluetooth.7d7857ee.js" as="script"><link rel="preload" href="/2024-06-21_23-30-58/assets/js/499.30ded4cf.js" as="script"><link rel="prefetch" href="/2024-06-21_23-30-58/assets/js/docs/api/index.b300ed27.js"><link rel="prefetch" href="/2024-06-21_23-30-58/assets/js/docs/component/index.2461606c.js"><link rel="prefetch" href="/2024-06-21_23-30-58/assets/js/docs/index.35fe9fe0.js"><link rel="prefetch" href="/2024-06-21_23-30-58/assets/js/docs/plugin/index.f9393cdf.js"><link rel="prefetch" href="/2024-06-21_23-30-58/assets/js/docs/tutorial/index.5d02b932.js"><link rel="prefetch" href="/2024-06-21_23-30-58/assets/js/docs/uni-app-x/index.0eaac43e.js"><link rel="prefetch" href="/2024-06-21_23-30-58/assets/js/docs/uniCloud/index.3b5d04be.js"><link rel="prefetch" href="/2024-06-21_23-30-58/assets/js/docs/worktile/auto/hbuilderx-extension/index.070be90b.js"><link rel="prefetch" href="/2024-06-21_23-30-58/assets/js/docs/worktile/index.95d0384b.js"><link rel="prefetch" href="/2024-06-21_23-30-58/assets/js/layout-Layout.18502495.js"><link rel="prefetch" href="/2024-06-21_23-30-58/assets/js/layout-NotFound.8e4868a1.js"><link rel="prefetch" href="/2024-06-21_23-30-58/assets/js/layout-SimpleLayout.abb4af07.js">
    <link rel="stylesheet" href="/2024-06-21_23-30-58/assets/css/0.styles.3a3b1b19.css">
  </head>
  <body>
    <div id="app" data-server-rendered="true"><div class="theme-container"><header class="navbar"><div class="main-navbar"><a href="https://www.dcloud.io" class="home-link"><img src="https://web-assets.dcloud.net.cn/unidoc/zh/logo-en-D.png" alt="uni-app" class="logo"> <img src="https://web-assets.dcloud.net.cn/unidoc/zh/logo-en.png" alt="uni-app" class="title-logo can-hide"></a> <div class="main-navbar-links can-hide"><div class="main-navbar-item active"><a href="javascript:;">uni-app</a></div><div class="main-navbar-item"><div class="main-navbar-link"><a href="https://doc.dcloud.net.cn/uni-app-x/" target="_blank" rel="noopener noreferrer" class="nav-link external">
  uni-app x
  <!----></a></div></div><div class="main-navbar-item"><div class="main-navbar-link"><a href="https://doc.dcloud.net.cn/uniCloud/" target="_blank" rel="noopener noreferrer" class="nav-link external">
  uniCloud
  <!----></a></div></div><div class="main-navbar-item"><div class="main-navbar-link"><a href="https://www.dcloud.io/hbuilderx.html?lang=en" target="_blank" rel="noopener noreferrer" class="nav-link external">
  HBuilder
  <!----></a></div></div><div class="main-navbar-item"><div class="main-navbar-link"><a href="https://nativesupport.dcloud.net.cn/README" target="_blank" rel="noopener noreferrer" class="nav-link external">
  uniMPSdk
  <!----></a></div></div></div> <div class="mobile-main-navbar"><div class="mobile-links_mobile"><a href="javascript:;" class="mobile-links__btn">uni-app</a></div> <div class="mobile-links__panel"><div class="main-navbar-item active"><a href="javascript:;">uni-app</a></div><div class="main-navbar-item"><div class="main-navbar-link"><a href="https://doc.dcloud.net.cn/uni-app-x/" target="_blank" rel="noopener noreferrer" class="nav-link external">
  uni-app x
  <!----></a></div></div><div class="main-navbar-item"><div class="main-navbar-link"><a href="https://doc.dcloud.net.cn/uniCloud/" target="_blank" rel="noopener noreferrer" class="nav-link external">
  uniCloud
  <!----></a></div></div><div class="main-navbar-item"><div class="main-navbar-link"><a href="https://www.dcloud.io/hbuilderx.html?lang=en" target="_blank" rel="noopener noreferrer" class="nav-link external">
  HBuilder
  <!----></a></div></div><div class="main-navbar-item"><div class="main-navbar-link"><a href="https://nativesupport.dcloud.net.cn/README" target="_blank" rel="noopener noreferrer" class="nav-link external">
  uniMPSdk
  <!----></a></div></div></div></div> <div class="dropdown-language"><div style="display: flex;align-items: center;"><span>English</span> <svg t="1629441415944" viewBox="0 20 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3713" width="16" height="16" class="icon"><path d="M508.025406 655.446718c-14.45307 0-28.183486-5.781228-39.023289-15.898376l-231.249118-231.249118c-10.117149-10.117149-10.117149-26.015526 0-36.132675s26.015526-10.117149 36.132675 0l231.249118 231.249118c2.16796 2.16796 4.335921 2.16796 5.781228 0l231.971771-231.971771c10.117149-10.117149 26.015526-10.117149 35.410021 0 10.117149 10.117149 10.117149 26.015526 0 36.132674l-231.971771 231.971772c-9.394495 10.117149-23.124912 15.898377-38.300635 15.898376z" p-id="3714"></path></svg></div> <!----></div> <div class="links" style="top:0px;z-index:100;"><!----> <div id="docsearch"><button type="button" aria-label="Search" class="DocSearch DocSearch-Button"><span class="DocSearch-Button-Container"><svg width="20" height="20" viewBox="0 0 20 20" class="DocSearch-Search-Icon"><path d="M14.386 14.386l4.0877 4.0877-4.0877-4.0877c-2.9418 2.9419-7.7115 2.9419-10.6533 0-2.9419-2.9418-2.9419-7.7115 0-10.6533 2.9418-2.9419 7.7115-2.9419 10.6533 0 2.9419 2.9418 2.9419 7.7115 0 10.6533z" stroke="currentColor" fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round"></path></svg> <span class="DocSearch-Button-Placeholder">Search</span></span> <span class="DocSearch-Button-Keys"><span class="DocSearch-Button-Key"><svg width="15" height="15" class="DocSearch-Control-Key-Icon"><path d="M4.505 4.496h2M5.505 5.496v5M8.216 4.496l.055 5.993M10 7.5c.333.333.5.667.5 1v2M12.326 4.5v5.996M8.384 4.496c1.674 0 2.116 0 2.116 1.5s-.442 1.5-2.116 1.5M3.205 9.303c-.09.448-.277 1.21-1.241 1.203C1 10.5.5 9.513.5 8V7c0-1.57.5-2.5 1.464-2.494.964.006 1.134.598 1.24 1.342M12.553 10.5h1.953" stroke-width="1.2" stroke="currentColor" fill="none" stroke-linecap="square"></path></svg></span> <span class="DocSearch-Button-Key">K</span></span></button></div></div></div> <div class="sub-navbar"><!----> <nav class="nav-links can-hide"><div class="nav-item"><a href="/" class="nav-link">
  Introduction
</a></div><div class="nav-item"><a href="/tutorial/" class="nav-link">
  Tutorial
</a></div><div class="nav-item"><a href="/collocation/pages.html" class="nav-link">
  Framework
</a></div><div class="nav-item"><a href="/component/" class="nav-link">
  Component
</a></div><div class="nav-item"><a href="/api/" class="nav-link router-link-active">
  API
</a></div><div class="nav-item"><a href="/plugin/" class="nav-link">
  Plugin
</a></div><div class="nav-item"><a href="/worktile/" class="nav-link">
  Worktile
</a></div><div class="nav-item"><a href="https://doc.dcloud.net.cn/uni-app-x/uts/" target="_blank" rel="noopener noreferrer" class="nav-link external">
  UTS
  <span link="https://doc.dcloud.net.cn/uni-app-x/uts/"><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg></span></a></div><div class="nav-item"><div class="dropdown-wrapper"><button type="button" aria-label="其他规范" class="dropdown-title"><span class="title">其他规范</span> <span class="arrow down"></span></button> <button type="button" aria-label="其他规范" class="mobile-dropdown-title"><span class="title">其他规范</span> <span class="arrow right"></span></button> <ul class="nav-dropdown" style="display:none;"><li class="dropdown-item"><!----> <a href="https://weexapp.com/" target="_blank" rel="noopener noreferrer" class="nav-link external">
  Weex
  <span link="https://weexapp.com/"><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg></span></a></li></ul></div></div><div class="nav-item"><a href="https://github.com/dcloudio/uni-app" target="_blank" rel="noopener noreferrer" class="nav-link external">
  GitHub
  <span link="https://github.com/dcloudio/uni-app"><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg></span></a></div></nav> <div class="mobile-sub-navbar"><div class="subnavbar__item"><a href="javascript:;">API</a></div></div></div></header> <div class="sidebar-mask"></div> <aside class="sidebar"><nav class="nav-links"><div class="nav-item"><a href="/" class="nav-link">
  Introduction
</a></div><div class="nav-item"><a href="/tutorial/" class="nav-link">
  Tutorial
</a></div><div class="nav-item"><a href="/collocation/pages.html" class="nav-link">
  Framework
</a></div><div class="nav-item"><a href="/component/" class="nav-link">
  Component
</a></div><div class="nav-item"><a href="/api/" class="nav-link router-link-active">
  API
</a></div><div class="nav-item"><a href="/plugin/" class="nav-link">
  Plugin
</a></div><div class="nav-item"><a href="/worktile/" class="nav-link">
  Worktile
</a></div><div class="nav-item"><a href="https://doc.dcloud.net.cn/uni-app-x/uts/" target="_blank" rel="noopener noreferrer" class="nav-link external">
  UTS
  <span link="https://doc.dcloud.net.cn/uni-app-x/uts/"><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg></span></a></div><div class="nav-item"><div class="dropdown-wrapper"><button type="button" aria-label="其他规范" class="dropdown-title"><span class="title">其他规范</span> <span class="arrow down"></span></button> <button type="button" aria-label="其他规范" class="mobile-dropdown-title"><span class="title">其他规范</span> <span class="arrow right"></span></button> <ul class="nav-dropdown" style="display:none;"><li class="dropdown-item"><!----> <a href="https://weexapp.com/" target="_blank" rel="noopener noreferrer" class="nav-link external">
  Weex
  <span link="https://weexapp.com/"><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg></span></a></li></ul></div></div><div class="nav-item"><a href="https://github.com/dcloudio/uni-app" target="_blank" rel="noopener noreferrer" class="nav-link external">
  GitHub
  <span link="https://github.com/dcloudio/uni-app"><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg></span></a></div></nav>  <ul class="sidebar-links"><li><a href="/api/" aria-current="page" class="sidebar-link data-no-emphasize">概述</a></li><li><section class="sidebar-group collapsable depth-0"><p class="sidebar-heading"><span>基础</span> <span class="arrow right"></span></p> <!----></section></li><li><section class="sidebar-group collapsable depth-0"><p class="sidebar-heading"><span>网络</span> <span class="arrow right"></span></p> <!----></section></li><li><section class="sidebar-group collapsable depth-0"><p class="sidebar-heading"><span>位置</span> <span class="arrow right"></span></p> <!----></section></li><li><section class="sidebar-group collapsable depth-0"><p class="sidebar-heading"><span>媒体</span> <span class="arrow right"></span></p> <!----></section></li><li><section class="sidebar-group collapsable depth-0"><p class="sidebar-heading open"><span>设备</span> <span class="arrow down"></span></p> <ul class="sidebar-links sidebar-group-items"><li><a href="/api/system/info.html" class="sidebar-link">uni.getSystemInfo</a></li><li><a href="/api/system/getDeviceInfo.html" class="sidebar-link">uni.getDeviceInfo</a></li><li><a href="/api/system/getWindowInfo.html" class="sidebar-link">uni.getWindowInfo</a></li><li><a href="/api/system/getAppBaseInfo.html" class="sidebar-link">uni.getAppBaseInfo</a></li><li><a href="/api/system/getappauthorizesetting.html" class="sidebar-link">uni.getAppAuthorizeSetting</a></li><li><a href="/api/system/getsystemsetting.html" class="sidebar-link">uni.getSystemSetting</a></li><li><a href="/api/system/openappauthorizesetting.html" class="sidebar-link">uni.openAppAuthorizeSetting</a></li><li><a href="/api/system/create-request-permission-listener.html" class="sidebar-link">uni.createRequestPermissionListener</a></li><li><a href="/api/system/memory.html" class="sidebar-link">内存</a></li><li><a href="/api/system/network.html" class="sidebar-link">网络状态</a></li><li><a href="/api/system/theme.html" class="sidebar-link">系统主题</a></li><li><a href="/api/system/accelerometer.html" class="sidebar-link">加速度计</a></li><li><a href="/api/system/compass.html" class="sidebar-link">罗盘</a></li><li><a href="/api/system/gyroscope.html" class="sidebar-link">陀螺仪</a></li><li><a href="/api/system/phone.html" class="sidebar-link">拨打电话</a></li><li><a href="/api/system/barcode.html" class="sidebar-link">扫码</a></li><li><a href="/api/system/clipboard.html" class="sidebar-link">剪贴板</a></li><li><a href="/api/system/brightness.html" class="sidebar-link">屏幕亮度</a></li><li><a href="/api/system/capture-screen.html" class="sidebar-link">用户截屏事件</a></li><li><a href="/api/system/vibrate.html" class="sidebar-link">振动</a></li><li><a href="/api/system/contact.html" class="sidebar-link">手机联系人</a></li><li><a href="/api/system/bluetooth.html" aria-current="page" class="active sidebar-link">蓝牙</a></li><li><a href="/api/system/ibeacon.html" class="sidebar-link">iBeacon</a></li><li><a href="/api/system/wifi.html" class="sidebar-link">Wi-Fi</a></li><li><a href="/api/system/nfc.html" class="sidebar-link">NFC</a></li><li><a href="/api/system/deviceMotion.html" class="sidebar-link">设备方向</a></li></ul></section></li><li><section class="sidebar-group collapsable depth-0"><a href="/api/worker" class="sidebar-heading clickable"><span>Worker</span> <span class="arrow right"></span></a> <!----></section></li><li><section class="sidebar-group collapsable depth-0"><p class="sidebar-heading"><span>界面</span> <span class="arrow right"></span></p> <!----></section></li><li><section class="sidebar-group collapsable depth-0"><p class="sidebar-heading"><span>广告</span> <span class="arrow right"></span></p> <!----></section></li><li><section class="sidebar-group collapsable depth-0"><p class="sidebar-heading"><span>第三方服务</span> <span class="arrow right"></span></p> <!----></section></li><li><a href="/api/uniCloud.html" class="sidebar-link">uniCloud</a></li><li><section class="sidebar-group collapsable depth-0"><p class="sidebar-heading"><span>其他</span> <span class="arrow right"></span></p> <!----></section></li><li><a href="/api/extapi.html" class="sidebar-link data-no-emphasize">uni ext api</a></li></ul>  <!----></aside> <main class="page"> <div class="table-of-contents" data-v-04c75cb3><div class="vuepress-toc-item-top vuepress-toc-h3" data-v-04c75cb3><a href="#openbluetoothadapter" title="uni.openBluetoothAdapter(OBJECT)" style="padding-left:0rem;" data-v-04c75cb3>
			uni.openBluetoothAdapter(OBJECT)
		</a></div><div class="vuepress-toc-item-top vuepress-toc-h4" data-v-04c75cb3><a href="#error" title="Error" style="padding-left:1rem;" data-v-04c75cb3>
			Error
		</a></div><div class="vuepress-toc-item-top vuepress-toc-h3" data-v-04c75cb3><a href="#startbluetoothdevicesdiscovery" title="uni.startBluetoothDevicesDiscovery(OBJECT)" style="padding-left:0rem;" data-v-04c75cb3>
			uni.startBluetoothDevicesDiscovery(OBJECT)
		</a></div><div class="vuepress-toc-item-top vuepress-toc-h4" data-v-04c75cb3><a href="#error-2" title="Error" style="padding-left:1rem;" data-v-04c75cb3>
			Error
		</a></div><div class="vuepress-toc-item-top vuepress-toc-h3" data-v-04c75cb3><a href="#onbluetoothdevicefound" title="uni.onBluetoothDeviceFound(CALLBACK)" style="padding-left:0rem;" data-v-04c75cb3>
			uni.onBluetoothDeviceFound(CALLBACK)
		</a></div><div class="vuepress-toc-item-top vuepress-toc-h3" data-v-04c75cb3><a href="#stopbluetoothdevicesdiscovery" title="uni.stopBluetoothDevicesDiscovery(OBJECT)" style="padding-left:0rem;" data-v-04c75cb3>
			uni.stopBluetoothDevicesDiscovery(OBJECT)
		</a></div><div class="vuepress-toc-item-top vuepress-toc-h4" data-v-04c75cb3><a href="#error-3" title="Error" style="padding-left:1rem;" data-v-04c75cb3>
			Error
		</a></div><div class="vuepress-toc-item-top vuepress-toc-h3" data-v-04c75cb3><a href="#onbluetoothadapterstatechange" title="uni.onBluetoothAdapterStateChange(CALLBACK)" style="padding-left:0rem;" data-v-04c75cb3>
			uni.onBluetoothAdapterStateChange(CALLBACK)
		</a></div><div class="vuepress-toc-item-top vuepress-toc-h3" data-v-04c75cb3><a href="#getconnectedbluetoothdevices" title="uni.getConnectedBluetoothDevices(OBJECT)" style="padding-left:0rem;" data-v-04c75cb3>
			uni.getConnectedBluetoothDevices(OBJECT)
		</a></div><div class="vuepress-toc-item-top vuepress-toc-h4" data-v-04c75cb3><a href="#error-4" title="Error" style="padding-left:1rem;" data-v-04c75cb3>
			Error
		</a></div> <span class="expand-button" data-v-04c75cb3>
		Expand All
		<span class="uni-icons uniui-bottom" data-v-04c75cb3></span></span></div> <div class="theme-default-content content__default"><div class="devsite-banner devsite-banner-translated" data-v-302b727a><div class="devsite-banner-message" data-v-302b727a><div class="devsite-banner-message-text" data-v-302b727a><span class="devsite-banner-translated-text" data-v-302b727a>
				This page is translated by
				<a href="//cloud.google.com/translate/" data-v-302b727a>Google Cloud Translation API</a></span></div> <a href="https://uniapp.dcloud.net.cn/api/system/bluetooth.html" data-label="Switch to Chinese" class="button" data-v-302b727a>Switch to Chinese</a></div></div> <p><strong>Platform difference description for Bluetooth APIs</strong></p> <table><thead><tr><th style="text-align:center;">App</th> <th style="text-align:center;">H5</th> <th style="text-align:center;">微信小程序</th> <th style="text-align:center;">支付宝小程序</th> <th style="text-align:center;">百度小程序</th> <th style="text-align:center;">抖音小程序</th> <th style="text-align:center;">飞书小程序</th> <th style="text-align:center;">QQ小程序</th> <th style="text-align:center;">快手小程序</th> <th style="text-align:center;">京东小程序</th></tr></thead> <tbody><tr><td style="text-align:center;">√</td> <td style="text-align:center;">x</td> <td style="text-align:center;">√</td> <td style="text-align:center;">√</td> <td style="text-align:center;">x</td> <td style="text-align:center;">x</td> <td style="text-align:center;">√</td> <td style="text-align:center;">x</td> <td style="text-align:center;">x</td> <td style="text-align:center;">√</td></tr></tbody></table> <h3 id="openbluetoothadapter"><a href="#openbluetoothadapter" class="header-anchor">#</a> uni.openBluetoothAdapter(OBJECT)</h3> <p>Initialize the Bluetooth module</p> <p><strong>OBJECT parameter description</strong></p> <table><thead><tr><th>Attribute</th> <th>Type</th> <th>Defaults</th> <th>Required</th> <th>Instruction</th></tr></thead> <tbody><tr><td>success</td> <td>function</td> <td></td> <td>No</td> <td>Callback function for successful interface calling</td></tr> <tr><td>fail</td> <td>function</td> <td></td> <td>No</td> <td>Callback function for failed interface calling</td></tr> <tr><td>complete</td> <td>function</td> <td></td> <td>No</td> <td>Callback function for closed interface calling (available both for successful and failed calling)</td></tr></tbody></table> <h4 id="error"><a href="#error" class="header-anchor">#</a> Error</h4> <table><thead><tr><th>Error code</th> <th>Error message</th> <th>Instruction</th></tr></thead> <tbody><tr><td>0</td> <td>ok</td> <td>Normal</td></tr> <tr><td>-1</td> <td>already connect</td> <td>connected</td></tr> <tr><td>10000</td> <td>not init</td> <td>Bluetooth is not initialized</td></tr> <tr><td>10001</td> <td>not available</td> <td>The bluetooth adapter is currently unavailable.</td></tr> <tr><td>10002</td> <td>no device</td> <td>Specified device not found</td></tr> <tr><td>10003</td> <td>connection fail</td> <td>Connection failed</td></tr> <tr><td>10004</td> <td>no service</td> <td>Specified service not found</td></tr> <tr><td>10005</td> <td>no characteristic</td> <td>Specified feature value not found</td></tr> <tr><td>10006</td> <td>no connection</td> <td>The current connection is disconnected</td></tr> <tr><td>10007</td> <td>property not support</td> <td>The current characteristic value does not support this operation.</td></tr> <tr><td>10008</td> <td>system error</td> <td>Exceptions reported by all other systems</td></tr> <tr><td>10009</td> <td>system not support</td> <td>Android system-specific. BLE is not available on system version lower than 4.3.</td></tr> <tr><td>10010</td> <td>already connect</td> <td>Connected</td></tr> <tr><td>10011</td> <td>need pin</td> <td>Pairing device requires pairing code</td></tr> <tr><td>10012</td> <td>operate time out</td> <td>Connection timed out</td></tr> <tr><td>10013</td> <td>invalid_data</td> <td>The connection deviceId is empty or in an incorrect format</td></tr></tbody></table> <p><strong>Notice</strong></p> <ul><li>Other Bluetooth-related APIs must be used after calling <a href="/api/system/bluetooth#openbluetoothadapter"><code>uni.openBluetoothAdapter</code></a>. Otherwise, the API will return an error (errCode=10000).</li> <li>If the Bluetooth of the user is disabled or the mobile phone does not support Bluetooth, calling <a href="/api/system/bluetooth#openbluetoothadapter"><code>uni.openBluetoothAdapter</code></a> will return an error (errCode=10001), indicating that the Bluetooth function of the mobile phone is unavailable. At this time, the Bluetooth module on the APP has been initialized. You can listen to the changes of Bluetooth status of mobile phone through <a href="/api/system/bluetooth#onbluetoothadapterstatechange"><code>uni.onBluetoothAdapterStateChange</code></a> or call all the APIs of the Bluetooth module.</li></ul> <p><strong>Sample code</strong></p> <div class="language-javascript extra-class"><pre class="language-javascript"><code>uni<span class="token punctuation">.</span><span class="token function">openBluetoothAdapter</span><span class="token punctuation">(</span><span class="token punctuation">{</span>
  <span class="token function">success</span><span class="token punctuation">(</span><span class="token parameter">res</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
    console<span class="token punctuation">.</span><span class="token function">log</span><span class="token punctuation">(</span>res<span class="token punctuation">)</span>
  <span class="token punctuation">}</span>
<span class="token punctuation">}</span><span class="token punctuation">)</span>
</code></pre></div><h3 id="startbluetoothdevicesdiscovery"><a href="#startbluetoothdevicesdiscovery" class="header-anchor">#</a> uni.startBluetoothDevicesDiscovery(OBJECT)</h3> <p>Start to search for nearby Bluetooth peripherals. <strong>Such an operation consumes system resources. Please call the <a href="/api/system/bluetooth#stopbluetoothdevicesdiscovery"><code>uni.stopBluetoothDevicesDiscovery</code></a> method to stop searching after searching and connecting to the device.</strong></p> <p><strong>OBJECT parameter description</strong></p> <table><thead><tr><th>Attribute</th> <th>Type</th> <th>Defaults</th> <th>Required</th> <th>Instruction</th></tr></thead> <tbody><tr><td>services</td> <td>Array&lt;String&gt;</td> <td></td> <td>No</td> <td>List of uuids of the bluetooth device primary service to search for. Some bluetooth devices broadcast the uuid of their own primary service. If this parameter is set, only the bluetooth devices whose broadcast packets have the corresponding uuid's main service will be searched. It is recommended to mainly filter out other Bluetooth devices that do not need to be processed through this parameter.</td></tr> <tr><td>allowDuplicatesKey</td> <td>boolean</td> <td>false</td> <td>No</td> <td>Whether to allow repeatedly reporting the same device. If repeated reporting is allowed, the <code>uni.onBlueToothDeviceFound</code> method will report the same device multiple times but with a different RSSI value each time.</td></tr> <tr><td>interval</td> <td>number</td> <td>0</td> <td>No</td> <td>Interval for reporting devices. 0 means reporting immediately when a new device is found, and other values means reporting according to the transition intervals.</td></tr> <tr><td>powerLevel</td> <td>string</td> <td>medium</td> <td>No</td> <td>Scanning mode, the higher the scanning speed, the more power consumption, only supported by Android. low: low, medium: medium, high: high. Only JD.com applet support</td></tr> <tr><td>success</td> <td>function</td> <td></td> <td>No</td> <td>Callback function for successful interface calling</td></tr> <tr><td>fail</td> <td>function</td> <td></td> <td>No</td> <td>Callback function for failed interface calling</td></tr> <tr><td>complete</td> <td>function</td> <td></td> <td>No</td> <td>Callback function for closed interface calling (available both for successful and failed calling)</td></tr></tbody></table> <h4 id="error-2"><a href="#error-2" class="header-anchor">#</a> Error</h4> <table><thead><tr><th>Error code</th> <th>Error message</th> <th>Instruction</th></tr></thead> <tbody><tr><td>0</td> <td>ok</td> <td>Normal</td></tr> <tr><td>10000</td> <td>not init</td> <td>Bluetooth is not initialized</td></tr> <tr><td>10001</td> <td>not available</td> <td>The bluetooth adapter is currently unavailable.</td></tr> <tr><td>10002</td> <td>no device</td> <td>Specified device not found</td></tr> <tr><td>10003</td> <td>connection fail</td> <td>Connection failed</td></tr> <tr><td>10004</td> <td>no service</td> <td>Specified service not found</td></tr> <tr><td>10005</td> <td>no characteristic</td> <td>Specified feature value not found</td></tr> <tr><td>10006</td> <td>no connection</td> <td>The current connection is disconnected</td></tr> <tr><td>10007</td> <td>property not support</td> <td>The current characteristic value does not support this operation.</td></tr> <tr><td>10008</td> <td>system error</td> <td>Exceptions reported by all other systems</td></tr> <tr><td>10009</td> <td>system not support</td> <td>Android system-specific. BLE is not available on system version lower than 4.3.</td></tr> <tr><td>10010</td> <td>already connect</td> <td>Connected</td></tr> <tr><td>10011</td> <td>need pin</td> <td>Pairing device requires pairing code</td></tr> <tr><td>10012</td> <td>operate time out</td> <td>Connection timed out</td></tr> <tr><td>10013</td> <td>invalid_data</td> <td>The connection deviceId is empty or in an incorrect format</td></tr></tbody></table> <p><strong>Notice:</strong></p> <ul><li>The App side currently only supports the discovery of ble Bluetooth devices. For more Bluetooth device discovery, you can use Native.js, refer to: [https://ask.dcloud.net.cn/article/114](https://ask.dcloud .net.cn/article/114). You can also get the [native plugin] in the plugin market (https://ext.dcloud.net.cn/search?q=%E8%93%9D%E7%89%99&amp;cat1=5&amp;cat2=51&amp;orderBy=UpdatedDate)</li></ul> <p><strong>Sample code</strong></p> <div class="language-javascript extra-class"><pre class="language-javascript"><code><span class="token comment">// Take the Bluetooth smart light of the WeChat hardware platform as an example, the UUID of the main service is FEE7. Pass in this parameter to only search for devices whose main service UUID is FEE7</span>
uni<span class="token punctuation">.</span><span class="token function">startBluetoothDevicesDiscovery</span><span class="token punctuation">(</span><span class="token punctuation">{</span>
  <span class="token literal-property property">services</span><span class="token operator">:</span> <span class="token punctuation">[</span><span class="token string">'FEE7'</span><span class="token punctuation">]</span><span class="token punctuation">,</span>
  <span class="token function">success</span><span class="token punctuation">(</span><span class="token parameter">res</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
    console<span class="token punctuation">.</span><span class="token function">log</span><span class="token punctuation">(</span>res<span class="token punctuation">)</span>
  <span class="token punctuation">}</span>
<span class="token punctuation">}</span><span class="token punctuation">)</span>
</code></pre></div><h3 id="onbluetoothdevicefound"><a href="#onbluetoothdevicefound" class="header-anchor">#</a> uni.onBluetoothDeviceFound(CALLBACK)</h3> <p>listen to the event of finding a new device</p> <p><strong>CALLBACK return parameter</strong></p> <table><thead><tr><th>Attribute</th> <th>Type</th> <th>Instruction</th></tr></thead> <tbody><tr><td>devices</td> <td>Array&lt;Object&gt;</td> <td>List of newly searched devices</td></tr></tbody></table> <p><strong>Structure of devices</strong></p> <table><thead><tr><th>Attribute</th> <th>Type</th> <th>Instruction</th></tr></thead> <tbody><tr><td>name</td> <td>string</td> <td>Bluetooth device name. Some devices may not have a name</td></tr> <tr><td>deviceId</td> <td>string</td> <td>Id used to distinguish the devices</td></tr> <tr><td>RSSI</td> <td>number</td> <td>Signal strength of the current Bluetooth device</td></tr> <tr><td>advertisData</td> <td>ArrayBuffer</td> <td>ManufacturerData segment in the broadcast data segment of the current Bluetooth device.</td></tr> <tr><td>advertisServiceUUIDs</td> <td>Array&lt;String&gt;</td> <td>ServiceUUIDs data segment in broadcast data segment of the current Bluetooth device</td></tr> <tr><td>localName</td> <td>string</td> <td>The LocalName data segment in the broadcast data segment of the current Bluetooth device</td></tr> <tr><td>serviceData</td> <td>Object</td> <td>The ServiceData data segment in the broadcast data segment of the current Bluetooth device, the JD applet does not support</td></tr></tbody></table> <p><strong>Notice</strong></p> <ul><li>If a device is called back in <a href="/api/system/bluetooth#onbluetoothdevicefound"><code>uni.onBluetoothDeviceFound</code></a>, it will be added to the array obtained by the <a href="/api/system/bluetooth#getbluetoothdevices"><code>uni.getBluetoothDevices</code></a> interface.</li></ul> <p><strong>Sample code</strong></p> <div class="language-javascript extra-class"><pre class="language-javascript"><code><span class="token comment">// Example for ArrayBuffer transformed to hexadecimal string</span>
<span class="token keyword">function</span> <span class="token function">ab2hex</span><span class="token punctuation">(</span><span class="token parameter">buffer</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
  <span class="token keyword">const</span> hexArr <span class="token operator">=</span> <span class="token class-name">Array</span><span class="token punctuation">.</span>prototype<span class="token punctuation">.</span><span class="token function">map</span><span class="token punctuation">.</span><span class="token function">call</span><span class="token punctuation">(</span>
    <span class="token keyword">new</span> <span class="token class-name">Uint8Array</span><span class="token punctuation">(</span>buffer<span class="token punctuation">)</span><span class="token punctuation">,</span>
    <span class="token keyword">function</span> <span class="token punctuation">(</span><span class="token parameter">bit</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
      <span class="token keyword">return</span> <span class="token punctuation">(</span><span class="token string">'00'</span> <span class="token operator">+</span> bit<span class="token punctuation">.</span><span class="token function">toString</span><span class="token punctuation">(</span><span class="token number">16</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">.</span><span class="token function">slice</span><span class="token punctuation">(</span><span class="token operator">-</span><span class="token number">2</span><span class="token punctuation">)</span>
    <span class="token punctuation">}</span>
  <span class="token punctuation">)</span>
  <span class="token keyword">return</span> hexArr<span class="token punctuation">.</span><span class="token function">join</span><span class="token punctuation">(</span><span class="token string">''</span><span class="token punctuation">)</span>
<span class="token punctuation">}</span>
uni<span class="token punctuation">.</span><span class="token function">onBluetoothDeviceFound</span><span class="token punctuation">(</span><span class="token keyword">function</span> <span class="token punctuation">(</span><span class="token parameter">devices</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
  console<span class="token punctuation">.</span><span class="token function">log</span><span class="token punctuation">(</span><span class="token string">'new device list has founded'</span><span class="token punctuation">)</span>
  console<span class="token punctuation">.</span><span class="token function">dir</span><span class="token punctuation">(</span>devices<span class="token punctuation">)</span>
  console<span class="token punctuation">.</span><span class="token function">log</span><span class="token punctuation">(</span><span class="token function">ab2hex</span><span class="token punctuation">(</span>devices<span class="token punctuation">[</span><span class="token number">0</span><span class="token punctuation">]</span><span class="token punctuation">.</span>advertisData<span class="token punctuation">)</span><span class="token punctuation">)</span>
<span class="token punctuation">}</span><span class="token punctuation">)</span>
</code></pre></div><h3 id="stopbluetoothdevicesdiscovery"><a href="#stopbluetoothdevicesdiscovery" class="header-anchor">#</a> uni.stopBluetoothDevicesDiscovery(OBJECT)</h3> <p>Stop searching for nearby Bluetooth peripherals. If the required Bluetooth device that has been found needn't to continue searching, it is recommended to call this interface to stop Bluetooth searching.</p> <p><strong>OBJECT parameter description</strong></p> <table><thead><tr><th>Attribute</th> <th>Type</th> <th>Defaults</th> <th>Required</th> <th>Instruction</th></tr></thead> <tbody><tr><td>success</td> <td>function</td> <td></td> <td>No</td> <td>Callback function for successful interface calling</td></tr> <tr><td>fail</td> <td>function</td> <td></td> <td>No</td> <td>Callback function for failed interface calling</td></tr> <tr><td>complete</td> <td>function</td> <td></td> <td>No</td> <td>Callback function for closed interface calling (available both for successful and failed calling)</td></tr></tbody></table> <h4 id="error-3"><a href="#error-3" class="header-anchor">#</a> Error</h4> <table><thead><tr><th>Error code</th> <th>Error message</th> <th>Instruction</th></tr></thead> <tbody><tr><td>0</td> <td>ok</td> <td>Normal</td></tr> <tr><td>10000</td> <td>not init</td> <td>Bluetooth is not initialized</td></tr> <tr><td>10001</td> <td>not available</td> <td>The bluetooth adapter is currently unavailable.</td></tr> <tr><td>10002</td> <td>no device</td> <td>Specified device not found</td></tr> <tr><td>10003</td> <td>connection fail</td> <td>Connection failed</td></tr> <tr><td>10004</td> <td>no service</td> <td>Specified service not found</td></tr> <tr><td>10005</td> <td>no characteristic</td> <td>Specified feature value not found</td></tr> <tr><td>10006</td> <td>no connection</td> <td>The current connection is disconnected</td></tr> <tr><td>10007</td> <td>property not support</td> <td>The current characteristic value does not support this operation.</td></tr> <tr><td>10008</td> <td>system error</td> <td>Exceptions reported by all other systems</td></tr> <tr><td>10009</td> <td>system not support</td> <td>Android system-specific. BLE is not available on system version lower than 4.3.</td></tr> <tr><td>10010</td> <td>already connect</td> <td>Connected</td></tr> <tr><td>10011</td> <td>need pin</td> <td>Pairing device requires pairing code</td></tr> <tr><td>10012</td> <td>operate time out</td> <td>Connection timed out</td></tr> <tr><td>10013</td> <td>invalid_data</td> <td>The connection deviceId is empty or in an incorrect format</td></tr></tbody></table> <p><strong>Sample code</strong></p> <div class="language-javascript extra-class"><pre class="language-javascript"><code>uni<span class="token punctuation">.</span><span class="token function">stopBluetoothDevicesDiscovery</span><span class="token punctuation">(</span><span class="token punctuation">{</span>
  <span class="token function">success</span><span class="token punctuation">(</span><span class="token parameter">res</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
    console<span class="token punctuation">.</span><span class="token function">log</span><span class="token punctuation">(</span>res<span class="token punctuation">)</span>
  <span class="token punctuation">}</span>
<span class="token punctuation">}</span><span class="token punctuation">)</span>
</code></pre></div><h3 id="onbluetoothadapterstatechange"><a href="#onbluetoothadapterstatechange" class="header-anchor">#</a> uni.onBluetoothAdapterStateChange(CALLBACK)</h3> <p>listen to Bluetooth adapter status change events</p> <p><strong>CALLBACK return parameter</strong></p> <table><thead><tr><th>Attribute</th> <th>Type</th> <th>Instruction</th></tr></thead> <tbody><tr><td>available</td> <td>boolean</td> <td>Is the Bluetooth adapter available?</td></tr> <tr><td>discovering</td> <td>boolean</td> <td>Whether the Bluetooth adapter is in the searching state</td></tr></tbody></table> <p><strong>Sample code</strong></p> <div class="language-javascript extra-class"><pre class="language-javascript"><code>uni<span class="token punctuation">.</span><span class="token function">onBluetoothAdapterStateChange</span><span class="token punctuation">(</span><span class="token keyword">function</span> <span class="token punctuation">(</span><span class="token parameter">res</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
  console<span class="token punctuation">.</span><span class="token function">log</span><span class="token punctuation">(</span><span class="token string">'adapterState changed, now is'</span><span class="token punctuation">,</span> res<span class="token punctuation">)</span>
<span class="token punctuation">}</span><span class="token punctuation">)</span>
</code></pre></div><h3 id="getconnectedbluetoothdevices"><a href="#getconnectedbluetoothdevices" class="header-anchor">#</a> uni.getConnectedBluetoothDevices(OBJECT)</h3> <p>Obtain the devices with connected status according to uuid.</p> <p><strong>OBJECT parameter description</strong></p> <table><thead><tr><th>Attribute</th> <th>Type</th> <th>Defaults</th> <th>Required</th> <th>Instruction</th></tr></thead> <tbody><tr><td>services</td> <td>Array&lt;String&gt;</td> <td></td> <td>Yes</td> <td>uuid list of Bluetooth device main service</td></tr> <tr><td>success</td> <td>function</td> <td></td> <td>No</td> <td>Callback function for successful interface calling</td></tr> <tr><td>fail</td> <td>function</td> <td></td> <td>No</td> <td>Callback function for failed interface calling</td></tr> <tr><td>complete</td> <td>function</td> <td></td> <td>No</td> <td>Callback function for closed interface calling (available both for successful and failed calling)</td></tr></tbody></table> <p><strong>Success return parameter description:</strong></p> <table><thead><tr><th>Attribute</th> <th>Type</th> <th>Instruction</th></tr></thead> <tbody><tr><td>devices</td> <td>Array&lt;Object&gt;</td> <td>List of searched devices</td></tr></tbody></table> <p><strong>Structure of res.devices</strong></p> <table><thead><tr><th>Attribute</th> <th>Type</th> <th>Instruction</th></tr></thead> <tbody><tr><td>name</td> <td>string</td> <td>Bluetooth device name. Some devices may not have a name</td></tr> <tr><td>deviceId</td> <td>string</td> <td>Id used to distinguish the devices</td></tr></tbody></table> <h4 id="error-4"><a href="#error-4" class="header-anchor">#</a> Error</h4> <table><thead><tr><th>Error code</th> <th>Error message</th> <th>Instruction</th></tr></thead> <tbody><tr><td>0</td> <td>ok</td> <td>Normal</td></tr> <tr><td>10000</td> <td>not init</td> <td>Bluetooth is not initialized</td></tr> <tr><td>10001</td> <td>not available</td> <td>The bluetooth adapter is currently unavailable.</td></tr> <tr><td>10002</td> <td>no device</td> <td>Specified device not found</td></tr> <tr><td>10003</td> <td>connection fail</td> <td>Connection failed</td></tr> <tr><td>10004</td> <td>no service</td> <td>Specified service not found</td></tr> <tr><td>10005</td> <td>no characteristic</td> <td>Specified feature value not found</td></tr> <tr><td>10006</td> <td>no connection</td> <td>The current connection is disconnected</td></tr> <tr><td>10007</td> <td>property not support</td> <td>The current characteristic value does not support this operation.</td></tr> <tr><td>10008</td> <td>system error</td> <td>Exceptions reported by all other systems</td></tr> <tr><td>10009</td> <td>system not support</td> <td>Android system-specific. BLE is not available on system version lower than 4.3.</td></tr> <tr><td>10010</td> <td>already connect</td> <td>Connected</td></tr> <tr><td>10011</td> <td>need pin</td> <td>Pairing device requires pairing code</td></tr> <tr><td>10012</td> <td>operate time out</td> <td>Connection timed out</td></tr> <tr><td>10013</td> <td>invalid_data</td> <td>The connection deviceId is empty or in an incorrect format</td></tr></tbody></table> <p><strong>Sample code</strong></p> <div class="language-javascript extra-class"><pre class="language-javascript"><code>uni<span class="token punctuation">.</span><span class="token function">getConnectedBluetoothDevices</span><span class="token punctuation">(</span><span class="token punctuation">{</span>
  <span class="token function">success</span><span class="token punctuation">(</span><span class="token parameter">res</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
    console<span class="token punctuation">.</span><span class="token function">log</span><span class="token punctuation">(</span>res<span class="token punctuation">)</span>
  <span class="token punctuation">}</span>
<span class="token punctuation">}</span><span class="token punctuation">)</span>
</code></pre></div><h3 id="getbluetoothdevices"><a href="#getbluetoothdevices" class="header-anchor">#</a> uni.getBluetoothDevices(OBJECT)</h3> <p>Get all discovered Bluetooth devices during the Bluetooth module's effective period. Devices that are already connected to the local PC are included.</p> <p><strong>OBJECT parameter description</strong></p> <table><thead><tr><th>Attribute</th> <th>Type</th> <th>Defaults</th> <th>Required</th> <th>Instruction</th></tr></thead> <tbody><tr><td>success</td> <td>function</td> <td></td> <td>No</td> <td>Callback function for successful interface calling</td></tr> <tr><td>fail</td> <td>function</td> <td></td> <td>No</td> <td>Callback function for failed interface calling</td></tr> <tr><td>complete</td> <td>function</td> <td></td> <td>No</td> <td>Callback function for closed interface calling (available both for successful and failed calling)</td></tr></tbody></table> <p><strong>Success return parameter description:</strong></p> <table><thead><tr><th style="text-align:left;">Attribute</th> <th style="text-align:left;">Type</th> <th style="text-align:left;">Instruction</th></tr></thead> <tbody><tr><td style="text-align:left;">devices</td> <td style="text-align:left;">Array&lt;Object&gt;</td> <td style="text-align:left;">List of connected devices corresponding to uuid</td></tr></tbody></table> <p><strong>Structure of res.devices</strong></p> <table><thead><tr><th>Attribute</th> <th>Type</th> <th>Instruction</th></tr></thead> <tbody><tr><td>name</td> <td>string</td> <td>Bluetooth device name. Some devices may not have a name</td></tr> <tr><td>deviceId</td> <td>string</td> <td>Id used to distinguish the devices</td></tr> <tr><td>RSSI</td> <td>number</td> <td>Signal strength of the current Bluetooth device</td></tr> <tr><td>advertisData</td> <td>ArrayBuffer</td> <td>ManufacturerData segment in the broadcast data segment of the current Bluetooth device.</td></tr> <tr><td>advertisServiceUUIDs</td> <td>Array&lt;String&gt;</td> <td>ServiceUUIDs data segment in broadcast data segment of the current Bluetooth device</td></tr> <tr><td>localName</td> <td>string</td> <td>The LocalName data segment in the broadcast data segment of the current Bluetooth device</td></tr> <tr><td>serviceData</td> <td>Object</td> <td>ServiceData data segment in broadcast data segment of the current Bluetooth device</td></tr></tbody></table> <h4 id="error-5"><a href="#error-5" class="header-anchor">#</a> Error</h4> <table><thead><tr><th>Error code</th> <th>Error message</th> <th>Instruction</th></tr></thead> <tbody><tr><td>0</td> <td>ok</td> <td>Normal</td></tr> <tr><td>10000</td> <td>not init</td> <td>Bluetooth is not initialized</td></tr> <tr><td>10001</td> <td>not available</td> <td>The bluetooth adapter is currently unavailable.</td></tr> <tr><td>10002</td> <td>no device</td> <td>Specified device not found</td></tr> <tr><td>10003</td> <td>connection fail</td> <td>Connection failed</td></tr> <tr><td>10004</td> <td>no service</td> <td>Specified service not found</td></tr> <tr><td>10005</td> <td>no characteristic</td> <td>Specified feature value not found</td></tr> <tr><td>10006</td> <td>no connection</td> <td>The current connection is disconnected</td></tr> <tr><td>10007</td> <td>property not support</td> <td>The current characteristic value does not support this operation.</td></tr> <tr><td>10008</td> <td>system error</td> <td>Exceptions reported by all other systems</td></tr> <tr><td>10009</td> <td>system not support</td> <td>Android system-specific. BLE is not available on system version lower than 4.3.</td></tr> <tr><td>10012</td> <td>operate time out</td> <td>Connection timed out</td></tr> <tr><td>10013</td> <td>invalid_data</td> <td>The connection deviceId is empty or in an incorrect format</td></tr></tbody></table> <p><strong>Sample code</strong></p> <div class="language-javascript extra-class"><pre class="language-javascript"><code><span class="token comment">// Example for ArrayBuffer transformed to hexadecimal string</span>
<span class="token keyword">function</span> <span class="token function">ab2hex</span><span class="token punctuation">(</span><span class="token parameter">buffer</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
  <span class="token keyword">const</span> hexArr <span class="token operator">=</span> <span class="token class-name">Array</span><span class="token punctuation">.</span>prototype<span class="token punctuation">.</span><span class="token function">map</span><span class="token punctuation">.</span><span class="token function">call</span><span class="token punctuation">(</span>
    <span class="token keyword">new</span> <span class="token class-name">Uint8Array</span><span class="token punctuation">(</span>buffer<span class="token punctuation">)</span><span class="token punctuation">,</span>
    <span class="token keyword">function</span> <span class="token punctuation">(</span><span class="token parameter">bit</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
      <span class="token keyword">return</span> <span class="token punctuation">(</span><span class="token string">'00'</span> <span class="token operator">+</span> bit<span class="token punctuation">.</span><span class="token function">toString</span><span class="token punctuation">(</span><span class="token number">16</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">.</span><span class="token function">slice</span><span class="token punctuation">(</span><span class="token operator">-</span><span class="token number">2</span><span class="token punctuation">)</span>
    <span class="token punctuation">}</span>
  <span class="token punctuation">)</span>
  <span class="token keyword">return</span> hexArr<span class="token punctuation">.</span><span class="token function">join</span><span class="token punctuation">(</span><span class="token string">''</span><span class="token punctuation">)</span>
<span class="token punctuation">}</span>
uni<span class="token punctuation">.</span><span class="token function">getBluetoothDevices</span><span class="token punctuation">(</span><span class="token punctuation">{</span>
  <span class="token function">success</span><span class="token punctuation">(</span><span class="token parameter">res</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
    console<span class="token punctuation">.</span><span class="token function">log</span><span class="token punctuation">(</span>res<span class="token punctuation">)</span>
    <span class="token keyword">if</span> <span class="token punctuation">(</span>res<span class="token punctuation">.</span>devices<span class="token punctuation">[</span><span class="token number">0</span><span class="token punctuation">]</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
      console<span class="token punctuation">.</span><span class="token function">log</span><span class="token punctuation">(</span><span class="token function">ab2hex</span><span class="token punctuation">(</span>res<span class="token punctuation">.</span>devices<span class="token punctuation">[</span><span class="token number">0</span><span class="token punctuation">]</span><span class="token punctuation">.</span>advertisData<span class="token punctuation">)</span><span class="token punctuation">)</span>
    <span class="token punctuation">}</span>
  <span class="token punctuation">}</span>
<span class="token punctuation">}</span><span class="token punctuation">)</span>
</code></pre></div><p><strong>Notice</strong></p> <ul><li>The list of devices obtained by this interface is <strong>. For all searched Bluetooth devices during the effective period of Bluetooth module</strong>, if <a href="/api/system/bluetooth#closebluetoothadapter"><code>uni.closeBluetoothAdapter</code></a> is not called to release resources in time after the end of the Bluetooth module use process, there will be a problem that calling this interface will return the Bluetooth devices searched in the previous Bluetooth usage process. At this point, such devices may no longer be with the user and cannot be connected.</li> <li>When a Bluetooth device is searched, the name field returned by the system is usually the device name in the LocalName field in the broadcast packet. However, if a connection is established with the Bluetooth device, the name field returned by the system will be changed to <code>GattName</code> obtained from the Bluetooth device. If the device name needs to be dynamically changed and displayed, the <code>localName</code> field is recommended.</li></ul> <h3 id="getbluetoothadapterstate"><a href="#getbluetoothadapterstate" class="header-anchor">#</a> uni.getBluetoothAdapterState(OBJECT)</h3> <p>Obtain the local Bluetooth adapter status.</p> <p><strong>OBJECT parameter description</strong></p> <table><thead><tr><th>Attribute</th> <th>Type</th> <th>Defaults</th> <th>Required</th> <th>Instruction</th></tr></thead> <tbody><tr><td>success</td> <td>function</td> <td></td> <td>No</td> <td>Callback function for successful interface calling</td></tr> <tr><td>fail</td> <td>function</td> <td></td> <td>No</td> <td>Callback function for failed interface calling</td></tr> <tr><td>complete</td> <td>function</td> <td></td> <td>No</td> <td>Callback function for closed interface calling (available both for successful and failed calling)</td></tr></tbody></table> <p><strong>Success return parameter description:</strong></p> <table><thead><tr><th>Attribute</th> <th>Type</th> <th>Instruction</th></tr></thead> <tbody><tr><td>discovering</td> <td>boolean</td> <td>Whether the device is being searched?</td></tr> <tr><td>available</td> <td>boolean</td> <td>Is the Bluetooth adapter available?</td></tr></tbody></table> <h4 id="error-6"><a href="#error-6" class="header-anchor">#</a> Error</h4> <table><thead><tr><th>Error code</th> <th>Error message</th> <th>Instruction</th></tr></thead> <tbody><tr><td>0</td> <td>ok</td> <td>Normal</td></tr> <tr><td>10000</td> <td>not init</td> <td>Bluetooth is not initialized</td></tr> <tr><td>10001</td> <td>not available</td> <td>The bluetooth adapter is currently unavailable.</td></tr> <tr><td>10002</td> <td>no device</td> <td>Specified device not found</td></tr> <tr><td>10003</td> <td>connection fail</td> <td>Connection failed</td></tr> <tr><td>10004</td> <td>no service</td> <td>Specified service not found</td></tr> <tr><td>10005</td> <td>no characteristic</td> <td>Specified feature value not found</td></tr> <tr><td>10006</td> <td>no connection</td> <td>The current connection is disconnected</td></tr> <tr><td>10007</td> <td>property not support</td> <td>The current characteristic value does not support this operation.</td></tr> <tr><td>10008</td> <td>system error</td> <td>Exceptions reported by all other systems</td></tr> <tr><td>10009</td> <td>system not support</td> <td>Android system-specific. BLE is not available on system version lower than 4.3.</td></tr> <tr><td>10010</td> <td>already connect</td> <td>Connected</td></tr> <tr><td>10011</td> <td>need pin</td> <td>Pairing device requires pairing code</td></tr> <tr><td>10012</td> <td>operate time out</td> <td>Connection timed out</td></tr> <tr><td>10013</td> <td>invalid_data</td> <td>The connection deviceId is empty or in an incorrect format</td></tr></tbody></table> <p><strong>Sample code</strong></p> <div class="language-javascript extra-class"><pre class="language-javascript"><code>uni<span class="token punctuation">.</span><span class="token function">getBluetoothAdapterState</span><span class="token punctuation">(</span><span class="token punctuation">{</span>
  <span class="token function">success</span><span class="token punctuation">(</span><span class="token parameter">res</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
    console<span class="token punctuation">.</span><span class="token function">log</span><span class="token punctuation">(</span>res<span class="token punctuation">)</span>
  <span class="token punctuation">}</span>
<span class="token punctuation">}</span><span class="token punctuation">)</span>
</code></pre></div><h3 id="closebluetoothadapter"><a href="#closebluetoothadapter" class="header-anchor">#</a> uni.closeBluetoothAdapter(OBJECT)</h3> <p>Disable the Bluetooth module. Calling this method will disconnect all established connections and release the system resources. It is recommended to call in pairs with <a href="/api/system/bluetooth#openbluetoothadapter"><code>uni.openBluetoothAdapter</code></a> after using the Bluetooth process.</p> <p><strong>OBJECT parameter description</strong></p> <table><thead><tr><th>Attribute</th> <th>Type</th> <th>Defaults</th> <th>Required</th> <th>Instruction</th></tr></thead> <tbody><tr><td>success</td> <td>function</td> <td></td> <td>No</td> <td>Callback function for successful interface calling</td></tr> <tr><td>fail</td> <td>function</td> <td></td> <td>No</td> <td>Callback function for failed interface calling</td></tr> <tr><td>complete</td> <td>function</td> <td></td> <td>No</td> <td>Callback function for closed interface calling (available both for successful and failed calling)</td></tr></tbody></table> <h4 id="error-7"><a href="#error-7" class="header-anchor">#</a> Error</h4> <table><thead><tr><th>Error code</th> <th>Error message</th> <th>Instruction</th></tr></thead> <tbody><tr><td>0</td> <td>ok</td> <td>Normal</td></tr> <tr><td>10000</td> <td>not init</td> <td>Bluetooth is not initialized</td></tr> <tr><td>10001</td> <td>not available</td> <td>The bluetooth adapter is currently unavailable.</td></tr> <tr><td>10002</td> <td>no device</td> <td>Specified device not found</td></tr> <tr><td>10003</td> <td>connection fail</td> <td>Connection failed</td></tr> <tr><td>10004</td> <td>no service</td> <td>Specified service not found</td></tr> <tr><td>10005</td> <td>no characteristic</td> <td>Specified feature value not found</td></tr> <tr><td>10006</td> <td>no connection</td> <td>The current connection is disconnected</td></tr> <tr><td>10007</td> <td>property not support</td> <td>The current characteristic value does not support this operation.</td></tr> <tr><td>10008</td> <td>system error</td> <td>Exceptions reported by all other systems</td></tr> <tr><td>10009</td> <td>system not support</td> <td>Android system-specific. BLE is not available on system version lower than 4.3.</td></tr> <tr><td>10010</td> <td>already connect</td> <td>Connected</td></tr> <tr><td>10011</td> <td>need pin</td> <td>Pairing device requires pairing code</td></tr> <tr><td>10012</td> <td>operate time out</td> <td>Connection timed out</td></tr> <tr><td>10013</td> <td>invalid_data</td> <td>The connection deviceId is empty or in an incorrect format</td></tr></tbody></table> <p><strong>Sample code</strong></p> <div class="language-javascript extra-class"><pre class="language-javascript"><code>uni<span class="token punctuation">.</span><span class="token function">closeBluetoothAdapter</span><span class="token punctuation">(</span><span class="token punctuation">{</span>
  <span class="token function">success</span><span class="token punctuation">(</span><span class="token parameter">res</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
    console<span class="token punctuation">.</span><span class="token function">log</span><span class="token punctuation">(</span>res<span class="token punctuation">)</span>
  <span class="token punctuation">}</span>
<span class="token punctuation">}</span><span class="token punctuation">)</span>
</code></pre></div></div> <footer class="page-edit"><div class="edit-link"><a href="https://github.com/dcloudio/unidocs-en/edit/main/docs/api/system/bluetooth.md" target="_blank" rel="noopener noreferrer">Edit this page on GitHub</a> <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg></span></div> <div class="last-updated"><span class="prefix">lastUpdated:</span> <span class="time">6/13/2023, 6:07:46 PM</span></div></footer> <!---->  <div id="footNavBox" data-v-f6aded24><div class="footNav" data-v-f6aded24><div id="footNavClassify" data-v-f6aded24><div class="footNavItem" data-v-f6aded24 data-v-f6aded24><div class="navItemTitle" data-v-f6aded24 data-v-f6aded24>products</div><div class="navLine" data-v-f6aded24 data-v-f6aded24></div><div class="navItemDetailBox" data-v-f6aded24 data-v-f6aded24><a target="_blank" href="https://www.dcloud.io/hbuilderx.html" class="navItemDetail" data-v-f6aded24 data-v-f6aded24>HBuilderX</a><a target="_blank" href="https://uniapp.dcloud.net.cn/" class="navItemDetail" data-v-f6aded24 data-v-f6aded24>uni-app</a><a target="_blank" href="https://uniapp.dcloud.net.cn/uniCloud/README" class="navItemDetail" data-v-f6aded24 data-v-f6aded24>uniCloud</a><a target="_blank" href="https://nativesupport.dcloud.net.cn/" class="navItemDetail" data-v-f6aded24 data-v-f6aded24>uniMPsdk</a><a target="_blank" href="https://www.dcloud.io/runtime.html" class="navItemDetail" data-v-f6aded24 data-v-f6aded24>5+Runtime</a><a target="_blank" href="https://uniapp.dcloud.net.cn/uniCloud/uni-id" class="navItemDetail" data-v-f6aded24 data-v-f6aded24>uni-id</a><a target="_blank" href="https://ext.dcloud.net.cn/plugin?id=3851" class="navItemDetail" data-v-f6aded24 data-v-f6aded24>uni-search</a><a target="_blank" href="https://uniapp.dcloud.net.cn/uniCloud/unipay" class="navItemDetail" data-v-f6aded24 data-v-f6aded24>uniPay</a><a target="_blank" href="https://uniapp.dcloud.net.cn/unipush" class="navItemDetail" data-v-f6aded24 data-v-f6aded24>uniPush</a><a target="_blank" href="https://uniapp.dcloud.io/univerify" class="navItemDetail" data-v-f6aded24 data-v-f6aded24>uni-verify</a><a target="_blank" href="https://uniapp.dcloud.net.cn/uniCloud/send-sms" class="navItemDetail" data-v-f6aded24 data-v-f6aded24>sms</a><a target="_blank" href="https://ext.dcloud.net.cn/plugin?id=5057" class="navItemDetail" data-v-f6aded24 data-v-f6aded24>uni-starter</a><a target="_blank" href="https://uniapp.dcloud.net.cn/uniCloud/admin" class="navItemDetail" data-v-f6aded24 data-v-f6aded24>uni-admin</a><a target="_blank" href="https://uniapp.dcloud.io/uniCloud/upgrade-center" class="navItemDetail" data-v-f6aded24 data-v-f6aded24>uni-upgrade-center</a><a target="_blank" href="https://uniapp.dcloud.net.cn/uniCloud/uni-im" class="navItemDetail" data-v-f6aded24 data-v-f6aded24>uni-im</a><a target="_blank" href="https://uniapp.dcloud.net.cn/uniCloud/uni-ai" class="navItemDetail" data-v-f6aded24 data-v-f6aded24>uni-ai</a><a target="_blank" href="https://uniapp.dcloud.net.cn/uniCloud/uni-cms" class="navItemDetail" data-v-f6aded24 data-v-f6aded24>uni-cms</a></div></div></div> <div id="aboutusBox" data-v-f6aded24><div class="footNavItem" data-v-f6aded24 data-v-f6aded24><div class="navItemTitle" data-v-f6aded24 data-v-f6aded24>Operating services</div><div class="navLine" data-v-f6aded24 data-v-f6aded24></div><div class="navItemDetailBox" data-v-f6aded24 data-v-f6aded24><a target="_blank" href="https://uniad.dcloud.net.cn/login" class="navItemDetail" data-v-f6aded24 data-v-f6aded24>uni-AD</a><a target="_blank" href="https://tongji.dcloud.net.cn/" class="navItemDetail" data-v-f6aded24 data-v-f6aded24>uni-stat</a><a target="_blank" href="https://www.dcloud.io/dportal.html" class="navItemDetail" data-v-f6aded24 data-v-f6aded24>uni-portal</a><a target="_blank" href="https://uniapp.dcloud.net.cn/tutorial/safe.html" class="navItemDetail" data-v-f6aded24 data-v-f6aded24>Safety topic</a></div></div><div class="footNavItem" data-v-f6aded24 data-v-f6aded24><div class="navItemTitle" data-v-f6aded24 data-v-f6aded24>Developer Services</div><div class="navLine" data-v-f6aded24 data-v-f6aded24></div><div class="navItemDetailBox" data-v-f6aded24 data-v-f6aded24><a target="_blank" href="https://ask.dcloud.net.cn/explore/" class="navItemDetail" data-v-f6aded24 data-v-f6aded24>forum</a><a target="_blank" href="https://dev.dcloud.net.cn/" class="navItemDetail" data-v-f6aded24 data-v-f6aded24>dashboard</a></div></div><div class="footNavItem" data-v-f6aded24 data-v-f6aded24><div class="navItemTitle" data-v-f6aded24 data-v-f6aded24>Docs</div><div class="navLine" data-v-f6aded24 data-v-f6aded24></div><div class="navItemDetailBox" data-v-f6aded24 data-v-f6aded24><a target="_blank" href="https://uniapp.dcloud.io/" class="navItemDetail" data-v-f6aded24 data-v-f6aded24>uni-app</a><a target="_blank" href="https://uniapp.dcloud.io/uniCloud/README" class="navItemDetail" data-v-f6aded24 data-v-f6aded24>uniCloud</a><a target="_blank" href="https://nativesupport.dcloud.net.cn/" class="navItemDetail" data-v-f6aded24 data-v-f6aded24>native-support</a><a target="_blank" href="https://hx.dcloud.net.cn/" class="navItemDetail" data-v-f6aded24 data-v-f6aded24>HBuilderX</a></div></div><div class="footNavItem" data-v-f6aded24 data-v-f6aded24><div class="navItemTitle" data-v-f6aded24 data-v-f6aded24>Ecosystem</div><div class="navLine" data-v-f6aded24 data-v-f6aded24></div><div class="navItemDetailBox" data-v-f6aded24 data-v-f6aded24><a target="_blank" href="https://ext.dcloud.net.cn/" class="navItemDetail" data-v-f6aded24 data-v-f6aded24>Marketplace</a><a target="_blank" href="https://open.dcloud.net.cn/pages/login/login" class="navItemDetail" data-v-f6aded24 data-v-f6aded24>OAuth</a></div></div></div></div> <div class="hbLogo" data-v-f6aded24></div> <div class="introduce" data-v-f6aded24><div class="introduce-item" data-v-f6aded24><span class="introduce-title" data-v-f6aded24>About：</span> <a href="https://dcloud.io/" target="_blank" class="navItemDetail" data-v-f6aded24>
						DCloud
					</a><a href="https://www.dcloud.io/hr/" target="_blank" class="navItemDetail" data-v-f6aded24>
						Jobs
					</a><a href="https://account.dcloud.io/privacy-policy/DCloud-Terms-of-Service.html" target="_blank" class="navItemDetail" data-v-f6aded24>
						Terms of service
					</a><a href="https://account.dcloud.io/privacy-policy/DCloud-Privacy-Policy.html" target="_blank" class="navItemDetail" data-v-f6aded24>
						Privacy policy
					</a><a href="https://account.dcloud.io/privacy-policy/DCloud-Acceptable-Use-Policy.html" target="_blank" class="navItemDetail" data-v-f6aded24>
						acceptable use policy
					</a></div><div class="introduce-item" data-v-f6aded24><span class="introduce-title" data-v-f6aded24>Contact us：</span> <a href="mailto:<EMAIL>" target="_blank" class="navItemDetail" data-v-f6aded24>
						bussiness：<EMAIL>
					</a><a href="mailto:<EMAIL>" target="_blank" class="navItemDetail" data-v-f6aded24>
						Advertising：<EMAIL>
					</a></div></div> <!----></div></main> <div class="sticker vuepress-toc"><h5>On This Page</h5> <div class="vuepress-toc-item vuepress-toc-h3 active"><a href="#openbluetoothadapter" title="uni.openBluetoothAdapter(OBJECT)" style="padding-left:0rem;">
			uni.openBluetoothAdapter(OBJECT)
		</a></div><div class="vuepress-toc-item vuepress-toc-h4"><a href="#error" title="Error" style="padding-left:1rem;">
			Error
		</a></div><div class="vuepress-toc-item vuepress-toc-h3"><a href="#startbluetoothdevicesdiscovery" title="uni.startBluetoothDevicesDiscovery(OBJECT)" style="padding-left:0rem;">
			uni.startBluetoothDevicesDiscovery(OBJECT)
		</a></div><div class="vuepress-toc-item vuepress-toc-h4"><a href="#error-2" title="Error" style="padding-left:1rem;">
			Error
		</a></div><div class="vuepress-toc-item vuepress-toc-h3"><a href="#onbluetoothdevicefound" title="uni.onBluetoothDeviceFound(CALLBACK)" style="padding-left:0rem;">
			uni.onBluetoothDeviceFound(CALLBACK)
		</a></div><div class="vuepress-toc-item vuepress-toc-h3"><a href="#stopbluetoothdevicesdiscovery" title="uni.stopBluetoothDevicesDiscovery(OBJECT)" style="padding-left:0rem;">
			uni.stopBluetoothDevicesDiscovery(OBJECT)
		</a></div><div class="vuepress-toc-item vuepress-toc-h4"><a href="#error-3" title="Error" style="padding-left:1rem;">
			Error
		</a></div><div class="vuepress-toc-item vuepress-toc-h3"><a href="#onbluetoothadapterstatechange" title="uni.onBluetoothAdapterStateChange(CALLBACK)" style="padding-left:0rem;">
			uni.onBluetoothAdapterStateChange(CALLBACK)
		</a></div><div class="vuepress-toc-item vuepress-toc-h3"><a href="#getconnectedbluetoothdevices" title="uni.getConnectedBluetoothDevices(OBJECT)" style="padding-left:0rem;">
			uni.getConnectedBluetoothDevices(OBJECT)
		</a></div><div class="vuepress-toc-item vuepress-toc-h4"><a href="#error-4" title="Error" style="padding-left:1rem;">
			Error
		</a></div><div class="vuepress-toc-item vuepress-toc-h3"><a href="#getbluetoothdevices" title="uni.getBluetoothDevices(OBJECT)" style="padding-left:0rem;">
			uni.getBluetoothDevices(OBJECT)
		</a></div><div class="vuepress-toc-item vuepress-toc-h4"><a href="#error-5" title="Error" style="padding-left:1rem;">
			Error
		</a></div><div class="vuepress-toc-item vuepress-toc-h3"><a href="#getbluetoothadapterstate" title="uni.getBluetoothAdapterState(OBJECT)" style="padding-left:0rem;">
			uni.getBluetoothAdapterState(OBJECT)
		</a></div><div class="vuepress-toc-item vuepress-toc-h4"><a href="#error-6" title="Error" style="padding-left:1rem;">
			Error
		</a></div><div class="vuepress-toc-item vuepress-toc-h3"><a href="#closebluetoothadapter" title="uni.closeBluetoothAdapter(OBJECT)" style="padding-left:0rem;">
			uni.closeBluetoothAdapter(OBJECT)
		</a></div><div class="vuepress-toc-item vuepress-toc-h4"><a href="#error-7" title="Error" style="padding-left:1rem;">
			Error
		</a></div></div></div><div class="global-ui"><!----></div></div>
    <script src="/2024-06-21_23-30-58/assets/js/app.deebca7c.js" defer></script><script src="/2024-06-21_23-30-58/assets/js/vendors~layout-Layout.44850949.js" defer></script><script src="/2024-06-21_23-30-58/assets/js/497.bff5d4d0.js" defer></script><script src="/2024-06-21_23-30-58/assets/js/docs/api/system/bluetooth.7d7857ee.js" defer></script><script src="/2024-06-21_23-30-58/assets/js/499.30ded4cf.js" defer></script>
  </body>
</html>
