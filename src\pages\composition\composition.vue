<template>
  <view class="composition-page">
    <view class="header">
      <text class="page-title">Composition API 示例</text>
    </view>

    <!-- 计数器组合式函数 -->
    <view class="section">
      <text class="section-title">useCounter 组合式函数</text>
      <view class="counter-display">
        <text class="count-text">计数: {{ count }}</text>
        <text class="info-text">双倍: {{ doubleCount }}</text>
        <text class="info-text">类型: {{ isEven ? '偶数' : '奇数' }}</text>
      </view>
      <view class="button-group">
        <button @click="increment" class="btn btn-primary">+1</button>
        <button @click="decrement" class="btn btn-secondary">-1</button>
        <button @click="reset" class="btn btn-default">重置</button>
        <button @click="setCount(10)" class="btn btn-info">设为10</button>
      </view>
    </view>

    <!-- 加载状态管理 -->
    <view class="section">
      <text class="section-title">useLoading 加载状态</text>
      <view class="loading-demo">
        <text class="status-text">
          状态: {{ loading ? '加载中...' : '空闲' }}
        </text>
        <view class="button-group">
          <button @click="startLoading" class="btn btn-primary">开始加载</button>
          <button @click="stopLoading" class="btn btn-secondary">停止加载</button>
          <button @click="simulateAsync" class="btn btn-info">模拟异步操作</button>
        </view>
      </view>
    </view>

    <!-- 生命周期钩子 -->
    <view class="section">
      <text class="section-title">生命周期钩子</text>
      <view class="lifecycle-logs">
        <text 
          v-for="(log, index) in lifecycleLogs" 
          :key="index" 
          class="log-item"
        >
          {{ log }}
        </text>
      </view>
    </view>

    <!-- 响应式引用 -->
    <view class="section">
      <text class="section-title">响应式引用 (ref & reactive)</text>
      <view class="reactive-demo">
        <view class="demo-item">
          <text class="label">ref 数据:</text>
          <input v-model="refData" class="input" placeholder="输入内容" />
          <text class="value">值: {{ refData }}</text>
        </view>
        <view class="demo-item">
          <text class="label">reactive 对象:</text>
          <input 
            v-model="reactiveData.name" 
            class="input" 
            placeholder="姓名" 
          />
          <input 
            v-model.number="reactiveData.age" 
            class="input" 
            placeholder="年龄" 
            type="number"
          />
          <text class="value">
            对象: {{ JSON.stringify(reactiveData) }}
          </text>
        </view>
      </view>
    </view>

    <!-- 计算属性和侦听器 -->
    <view class="section">
      <text class="section-title">计算属性和侦听器</text>
      <view class="computed-demo">
        <view class="demo-item">
          <text class="label">输入数字:</text>
          <input 
            v-model.number="inputNumber" 
            class="input" 
            type="number" 
            placeholder="输入数字"
          />
        </view>
        <view class="result">
          <text>平方: {{ squaredNumber }}</text>
          <text>立方: {{ cubedNumber }}</text>
          <text>变化次数: {{ changeCount }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { 
  ref, 
  reactive, 
  computed, 
  watch, 
  onMounted, 
  onUnmounted,
  onBeforeMount,
  onBeforeUnmount
} from 'vue'
import { useCounter } from '@/composables/useCounter'
import { useLoading } from '@/composables/useLoading'

// 使用计数器组合式函数
const { count, doubleCount, isEven, increment, decrement, reset, setCount } = useCounter(0)

// 使用加载状态组合式函数
const { loading, startLoading, stopLoading, withLoading } = useLoading()

// 模拟异步操作
const simulateAsync = async () => {
  await withLoading(async () => {
    // 模拟网络请求
    await new Promise(resolve => setTimeout(resolve, 2000))
    console.log('异步操作完成')
  })
}

// 生命周期日志
const lifecycleLogs = ref<string[]>([])

const addLog = (message: string) => {
  const timestamp = new Date().toLocaleTimeString()
  lifecycleLogs.value.push(`[${timestamp}] ${message}`)
}

// 响应式数据示例
const refData = ref('')
const reactiveData = reactive({
  name: '',
  age: 0
})

// 计算属性和侦听器示例
const inputNumber = ref(0)
const changeCount = ref(0)

const squaredNumber = computed(() => inputNumber.value ** 2)
const cubedNumber = computed(() => inputNumber.value ** 3)

// 侦听器
watch(inputNumber, (newVal, oldVal) => {
  changeCount.value++
  console.log(`数字从 ${oldVal} 变为 ${newVal}`)
})

// 生命周期钩子
onBeforeMount(() => {
  addLog('onBeforeMount: 组件挂载前')
})

onMounted(() => {
  addLog('onMounted: 组件已挂载')
})

onBeforeUnmount(() => {
  addLog('onBeforeUnmount: 组件卸载前')
})

onUnmounted(() => {
  addLog('onUnmounted: 组件已卸载')
})
</script>

<style scoped>
.composition-page {
  padding: 40rpx;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.page-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
}

.section {
  margin-bottom: 60rpx;
  padding: 30rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  display: block;
}

.counter-display {
  text-align: center;
  margin-bottom: 30rpx;
}

.count-text {
  font-size: 48rpx;
  color: #007aff;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}

.info-text {
  font-size: 28rpx;
  color: #666;
  display: block;
  margin-bottom: 5rpx;
}

.status-text {
  font-size: 32rpx;
  color: #333;
  display: block;
  text-align: center;
  margin-bottom: 20rpx;
}

.button-group {
  display: flex;
  justify-content: center;
  gap: 20rpx;
  flex-wrap: wrap;
}

.btn {
  padding: 20rpx 30rpx;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: white;
}

.btn-primary {
  background-color: #007aff;
}

.btn-secondary {
  background-color: #34c759;
}

.btn-default {
  background-color: #8e8e93;
}

.btn-info {
  background-color: #5ac8fa;
}

.lifecycle-logs {
  max-height: 400rpx;
  overflow-y: auto;
  background: white;
  border-radius: 8rpx;
  padding: 20rpx;
}

.log-item {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
  padding: 10rpx;
  background: #f0f0f0;
  border-radius: 4rpx;
}

.demo-item {
  margin-bottom: 30rpx;
}

.label {
  font-size: 28rpx;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.input {
  width: 100%;
  padding: 20rpx;
  border: 2rpx solid #ddd;
  border-radius: 8rpx;
  font-size: 28rpx;
  margin-bottom: 10rpx;
}

.value {
  font-size: 24rpx;
  color: #666;
  display: block;
}

.result {
  padding: 20rpx;
  background: #e3f2fd;
  border-radius: 8rpx;
}

.result text {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}
</style>
