{"recommendations": ["vue.volar", "vue.vscode-typescript-vue-plugin", "bradlc.vscode-tailwindcss", "esbenp.prettier-vscode", "dbaeumer.vscode-eslint", "ms-vscode.vscode-typescript-next", "formulahendry.auto-rename-tag", "christian-kohler.path-intellisense", "ms-vscode.vscode-json", "redhat.vscode-yaml", "ms-vscode.vscode-css-peek", "bradlc.vscode-tailwindcss", "usernamehw.errorlens", "gruntfuggly.todo-tree", "ms-vscode.vscode-typescript-next"]}