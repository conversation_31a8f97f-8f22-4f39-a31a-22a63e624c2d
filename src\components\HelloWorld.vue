<template>
  <view class="hello-world">
    <text class="title">{{ title }}</text>
    <text class="count">点击次数: {{ count }}</text>
    <button @click="increment" class="btn">点击我</button>
    <text class="message">{{ message }}</text>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// 定义props
interface Props {
  title?: string
}

const props = withDefaults(defineProps<Props>(), {
  title: 'Hello Vue 3!'
})

// 响应式数据
const count = ref(0)

// 计算属性
const message = computed(() => {
  return count.value > 5 ? '你点击了很多次！' : '继续点击试试'
})

// 方法
const increment = () => {
  count.value++
}
</script>

<style scoped>
.hello-world {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.count {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.btn {
  background-color: #007aff;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  margin-bottom: 20rpx;
}

.message {
  font-size: 24rpx;
  color: #999;
}
</style>
