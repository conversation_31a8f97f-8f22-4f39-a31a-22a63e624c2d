<template>
  <view class="demo-page">
    <view class="header">
      <text class="page-title">Vue 3 功能示例</text>
    </view>

    <!-- 响应式数据示例 -->
    <view class="section">
      <text class="section-title">响应式数据</text>
      <view class="demo-item">
        <text class="label">用户名:</text>
        <input 
          v-model="userInfo.name" 
          placeholder="请输入用户名" 
          class="input"
        />
      </view>
      <view class="demo-item">
        <text class="label">年龄:</text>
        <input 
          v-model.number="userInfo.age" 
          type="number" 
          placeholder="请输入年龄" 
          class="input"
        />
      </view>
      <view class="result">
        <text>用户信息: {{ userInfo.name }}, {{ userInfo.age }}岁</text>
      </view>
    </view>

    <!-- 计算属性示例 -->
    <view class="section">
      <text class="section-title">计算属性</text>
      <view class="demo-item">
        <text class="label">商品价格:</text>
        <input 
          v-model.number="price" 
          type="number" 
          placeholder="请输入价格" 
          class="input"
        />
      </view>
      <view class="demo-item">
        <text class="label">数量:</text>
        <input 
          v-model.number="quantity" 
          type="number" 
          placeholder="请输入数量" 
          class="input"
        />
      </view>
      <view class="result">
        <text>总价: ¥{{ totalPrice }}</text>
      </view>
    </view>

    <!-- 列表渲染示例 -->
    <view class="section">
      <text class="section-title">列表渲染</text>
      <view class="demo-item">
        <input 
          v-model="newTodo" 
          placeholder="添加待办事项" 
          class="input"
          @confirm="addTodo"
        />
        <button @click="addTodo" class="btn btn-primary">添加</button>
      </view>
      <view class="todo-list">
        <view 
          v-for="(todo, index) in todos" 
          :key="todo.id" 
          class="todo-item"
          :class="{ completed: todo.completed }"
        >
          <text @click="toggleTodo(index)" class="todo-text">
            {{ todo.text }}
          </text>
          <button @click="removeTodo(index)" class="btn btn-danger">删除</button>
        </view>
      </view>
    </view>

    <!-- 条件渲染示例 -->
    <view class="section">
      <text class="section-title">条件渲染</text>
      <view class="demo-item">
        <button @click="toggleVisible" class="btn btn-secondary">
          {{ isVisible ? '隐藏' : '显示' }}内容
        </button>
      </view>
      <view v-if="isVisible" class="conditional-content">
        <text>这是条件渲染的内容！</text>
      </view>
    </view>

    <!-- 事件处理示例 -->
    <view class="section">
      <text class="section-title">事件处理</text>
      <view class="demo-item">
        <button @click="handleClick" class="btn btn-primary">点击事件</button>
        <button @click="handleClickWithParam('参数')" class="btn btn-secondary">
          带参数点击
        </button>
      </view>
      <view class="result">
        <text>{{ eventMessage }}</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'

// 响应式数据
const userInfo = reactive({
  name: '',
  age: 0
})

// 计算属性示例
const price = ref(0)
const quantity = ref(0)
const totalPrice = computed(() => {
  return (price.value * quantity.value).toFixed(2)
})

// 列表渲染示例
const newTodo = ref('')
const todos = ref([
  { id: 1, text: '学习Vue 3', completed: false },
  { id: 2, text: '使用uniapp', completed: true },
  { id: 3, text: '开发小程序', completed: false }
])

let todoId = 4

const addTodo = () => {
  if (newTodo.value.trim()) {
    todos.value.push({
      id: todoId++,
      text: newTodo.value,
      completed: false
    })
    newTodo.value = ''
  }
}

const removeTodo = (index: number) => {
  todos.value.splice(index, 1)
}

const toggleTodo = (index: number) => {
  todos.value[index].completed = !todos.value[index].completed
}

// 条件渲染示例
const isVisible = ref(true)

const toggleVisible = () => {
  isVisible.value = !isVisible.value
}

// 事件处理示例
const eventMessage = ref('等待事件触发...')

const handleClick = () => {
  eventMessage.value = `点击事件触发于 ${new Date().toLocaleTimeString()}`
}

const handleClickWithParam = (param: string) => {
  eventMessage.value = `带参数的点击事件: ${param}`
}
</script>

<style scoped>
.demo-page {
  padding: 40rpx;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.page-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
}

.section {
  margin-bottom: 60rpx;
  padding: 30rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  display: block;
}

.demo-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  gap: 20rpx;
}

.label {
  font-size: 28rpx;
  color: #666;
  min-width: 120rpx;
}

.input {
  flex: 1;
  padding: 20rpx;
  border: 2rpx solid #ddd;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.btn {
  padding: 20rpx 30rpx;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: white;
}

.btn-primary {
  background-color: #007aff;
}

.btn-secondary {
  background-color: #34c759;
}

.btn-danger {
  background-color: #ff3b30;
}

.result {
  padding: 20rpx;
  background: #e3f2fd;
  border-radius: 8rpx;
  margin-top: 20rpx;
}

.todo-list {
  margin-top: 20rpx;
}

.todo-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background: white;
  border-radius: 8rpx;
  margin-bottom: 10rpx;
}

.todo-item.completed .todo-text {
  text-decoration: line-through;
  color: #999;
}

.todo-text {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.conditional-content {
  padding: 30rpx;
  background: #fff3cd;
  border-radius: 8rpx;
  margin-top: 20rpx;
  text-align: center;
}
</style>
