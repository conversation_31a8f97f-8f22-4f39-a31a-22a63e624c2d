{"version": "2.0.0", "tasks": [{"label": "dev:h5", "type": "shell", "command": "npm", "args": ["run", "dev:h5"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "dev:mp-weixin", "type": "shell", "command": "npm", "args": ["run", "dev:mp-weixin"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "build:h5", "type": "shell", "command": "npm", "args": ["run", "build:h5"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "lint", "type": "shell", "command": "npm", "args": ["run", "lint"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}]}