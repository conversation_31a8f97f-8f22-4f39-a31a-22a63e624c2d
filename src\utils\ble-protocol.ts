// ble-protocol.ts
import { Buffer } from "buffer";

/**
 * @file ble-protocol.ts
 * @description 定义了应用层分包协议的常量、类型和核心工具函数。
 */

// --- 协议常量定义 ---

/** 协议头部长度（字节） */
export const HEADER_LENGTH = 2;

/**
 * 数据类型枚举
 * 用于协议头部的 Type 字段，最多支持16种类型 (0-15)。
 */
export enum DataType {
  Command = 0,
  JSON = 0,
  File = 2,
  OTA = 3,
  // ...可以继续添加其他类型
}

// --- 协议核心工具 ---

/**
 * 封装（打包）函数
 * 将一个大的数据块（Buffer）根据指定的MTU大小，切分成符合TLVP协议的小数据包数组。
 *
 * @param data - 需要发送的原始数据 Buffer。
 * @param dataType - 数据的类型，来自 DataType 枚举。
 * @param mtu - BLE连接协商后的 ATT_MTU 值。注意：这里是ATT_MTU，而不是净荷长度。
 * @returns 返回一个 Buffer 数组，每个 Buffer 都是一个可以直接发送的完整数据包。
 */
export function pack(data: Buffer, dataType: DataType, mtu: number): Buffer[] {
  // 计算每个包的最大载荷（Payload）长度
  // GATT Notification 头部占用3字节，我们的协议头占用2字节
  const maxPayloadSize = mtu - 3 - HEADER_LENGTH;
  if (maxPayloadSize <= 0) {
    throw new Error("MTU值太小，无法承载任何数据。");
  }

  const totalPackets = Math.ceil(data.length / maxPayloadSize) || 1;
  const packets: Buffer[] = [];

  for (let i = 0; i < totalPackets; i++) {
    // --- 1. 构建协议头 (2字节) ---
    const header = Buffer.alloc(HEADER_LENGTH);

    const isStartPacket = i === 0;
    const isEndPacket = i === totalPackets - 1;

    // S (Start) bit: 位于第15位
    const sBit = isStartPacket ? 1 << 15 : 0;
    // E (End) bit: 位于第14位
    const eBit = isEndPacket ? 1 << 14 : 0;
    // Type: 位于第8-11位
    const typeBits = (dataType & 0x0f) << 8;
    // Packet Index: 位于第0-7位
    const indexBits = i & 0xff;

    // 使用位或运算组合成一个16位的头部
    const headerValue = sBit | eBit | typeBits | indexBits;

    // 将16位头部值以大端序写入Buffer
    header.writeUInt16BE(headerValue, 0);

    // --- 2. 切分数据载荷 (Payload) ---
    const start = i * maxPayloadSize;
    const end = Math.min(start + maxPayloadSize, data.length);
    const payload = data.slice(start, end);

    // --- 3. 组合成完整数据包 ---
    const finalPacket = Buffer.concat([header, payload]);
    packets.push(finalPacket);
  }

  return packets;
}

/**
 * 数据包解析与重组器
 * 这是一个状态机类，用于接收数据包并将其重组成原始数据。
 */
export class PacketReassembler {
  private chunks: Buffer[] = [];
  private isAssembling = false;
  private expectedIndex = 0;
  private totalLength = 0;
  private currentDataType: DataType | null = null;

  /**
   * 添加一个从蓝牙设备收到的数据包。
   * @param packet - 收到的原始数据包 Buffer。
   * @returns 如果数据传输完成，则返回包含完整数据和类型的对象；否则返回 null。
   */
  public addPacket(packet: Buffer): { type: DataType; data: Buffer } | null {
    if (packet.length < HEADER_LENGTH) {
      console.error("错误：收到的数据包太短，无法解析头部。");
      this.reset(); // 重置状态
      return null;
    }

    // --- 1. 解析协议头 ---
    const headerValue = packet.readUInt16BE(0);
    const isStartPacket = (headerValue & (1 << 15)) !== 0;
    const isEndPacket = (headerValue & (1 << 14)) !== 0;
    const packetType = (headerValue >> 8) & 0x0f;
    const packetIndex = headerValue & 0xff;

    // --- 2. 状态机逻辑 ---
    if (isStartPacket) {
      if (this.isAssembling) {
        console.warn("警告：在当前传输未完成时收到了新的开始包。旧的传输将被丢弃。");
      }
      this.reset(); // 开始新的传输，重置所有状态
      this.isAssembling = true;
      this.currentDataType = packetType;
      this.expectedIndex = 0;
    }

    if (!this.isAssembling) {
      console.warn("警告：收到了一个孤立的数据包（非开始包），已丢弃。");
      return null;
    }

    // 检查数据类型和包序号是否匹配
    if (packetType !== this.currentDataType) {
      console.error(
        `错误：数据类型不匹配！期望 ${this.currentDataType}, 收到 ${packetType}。传输失败。`
      );
      this.reset();
      return null;
    }
    if (packetIndex !== this.expectedIndex) {
      console.error(
        `错误：包序号不匹配！期望 ${this.expectedIndex}, 收到 ${packetIndex}。传输失败。`
      );
      this.reset();
      return null;
    }

    // --- 3. 存储数据载荷 ---
    const payload = packet.slice(HEADER_LENGTH);
    this.chunks.push(payload);
    this.totalLength += payload.length;
    this.expectedIndex++;

    // --- 4. 判断是否结束 ---
    if (isEndPacket) {
      const finalData = Buffer.concat(this.chunks, this.totalLength);
      const result = { type: this.currentDataType, data: finalData };

      console.log(
        `信息：数据重组完成！类型: ${DataType[result.type]}, 总大小: ${result.data.length}字节。`
      );

      this.reset(); // 完成后重置状态机，准备下一次接收
      return result;
    }

    return null; // 还需更多数据包
  }

  /**
   * 重置状态机，清空所有进行中的传输状态。
   */
  public reset(): void {
    this.chunks = [];
    this.isAssembling = false;
    this.expectedIndex = 0;
    this.totalLength = 0;
    this.currentDataType = null;
  }
}
