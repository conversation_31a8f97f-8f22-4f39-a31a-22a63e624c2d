<template>
  <view class="home-page">
    <!-- 背景视频区域 -->
    <view class="video-background">
      <view class="video-overlay"></view>
    </view>

    <!-- 内容区域 -->
    <view class="content-area">
      <!-- 顶部Logo和标题 -->
      <view class="header-section">
        <image class="main-logo" src="/static/logo.png" />
        <view class="title-group">
          <text class="main-title">工程机械远程智控专家</text>
          <text class="main-subtitle">拓无止境，智控无疆</text>
        </view>
      </view>

      <!-- 功能卡片区域 -->
      <view class="feature-cards">
        <view class="feature-card" @click="navigateToCase">
          <view class="card-icon">📱</view>
          <view class="card-content">
            <text class="card-title">精选案例</text>
            <text class="card-desc">查看我们的优秀作品</text>
          </view>
          <view class="card-arrow">›</view>
        </view>

        <view class="feature-card" @click="navigateToProfile">
          <view class="card-icon">👤</view>
          <view class="card-content">
            <text class="card-title">个人中心</text>
            <text class="card-desc">管理您的个人信息</text>
          </view>
          <view class="card-arrow">›</view>
        </view>

        <view class="feature-card" @click="showAbout">
          <view class="card-icon">ℹ️</view>
          <view class="card-content">
            <text class="card-title">关于我们</text>
            <text class="card-desc">了解更多产品信息</text>
          </view>
          <view class="card-arrow">›</view>
        </view>
      </view>

      <!-- 底部信息 -->
      <view class="footer-info">
        <text class="footer-text">© 2024 BUX微信小程序</text>
        <text class="footer-version">版本 v1.0.0</text>
      </view>
    </view>

    <!-- 自定义TabBar - 只在首页显示 -->
    <CustomTabBar :current="0" @change="onTabChange" />
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import CustomTabBar from '@/components/CustomTabBar.vue'

// 导航到案例页面
const navigateToCase = () => {
  uni.navigateTo({
    url: '/pages/case/case'
  })
}

// 导航到个人中心
const navigateToProfile = () => {
  uni.navigateTo({
    url: '/pages/profile/profile'
  })
}

// 显示关于我们
const showAbout = () => {
  uni.showModal({
    title: '关于我们',
    content:
      'BUX微信小程序是一个基于uniapp + Vue 3开发的多端应用，致力于为用户提供优质的移动端体验。\n\n我们专注于智能生活解决方案，通过创新的技术和设计，让科技更好地服务于生活。',
    showCancel: false,
    confirmText: '知道了'
  })
}

// TabBar切换处理
const onTabChange = (index: number) => {
  console.log('Tab changed to:', index)
  // TabBar组件内部已经处理了页面跳转
}
</script>

<style lang="scss" scoped>
.home-page {
  position: relative;
  min-height: 100vh;
  overflow: hidden;
}

.video-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;

  .background-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 95, 0, 0.8) 0%, rgba(255, 140, 0, 0.8) 100%);
  }
}

.content-area {
  position: relative;
  z-index: 1;
  min-height: 100vh;
  padding: 80rpx 40rpx 160rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.header-section {
  text-align: center;
  margin-bottom: 80rpx;

  .main-logo {
    width: 160rpx;
    height: 160rpx;
    border-radius: 32rpx;
    margin-bottom: 40rpx;
    margin-top: 80rpx;
    box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.2);
  }

  .title-group {
    .main-title {
      display: block;
      font-size: 56rpx;
      font-weight: 700;
      color: #fff;
      margin-bottom: 16rpx;
      text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.3);
    }

    .main-subtitle {
      display: block;
      font-size: 32rpx;
      color: rgba(255, 255, 255, 0.9);
      font-weight: 400;
      text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
    }
  }
}

.feature-cards {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  margin-bottom: 60rpx;

  .feature-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20rpx);
    border-radius: 24rpx;
    padding: 32rpx;
    display: flex;
    align-items: center;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
    border: 2rpx solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.98);
      box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
    }

    .card-icon {
      width: 80rpx;
      height: 80rpx;
      background: linear-gradient(135deg, #ff5f00 0%, #ff8c00 100%);
      border-radius: 20rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 32rpx;
      margin-right: 24rpx;
    }

    .card-content {
      flex: 1;

      .card-title {
        display: block;
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 8rpx;
      }

      .card-desc {
        display: block;
        font-size: 26rpx;
        color: #666;
        line-height: 1.4;
      }
    }

    .card-arrow {
      font-size: 32rpx;
      color: #ccc;
      font-weight: bold;
    }
  }
}

.footer-info {
  text-align: center;

  .footer-text {
    display: block;
    font-size: 24rpx;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 8rpx;
    text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
  }

  .footer-version {
    display: block;
    font-size: 22rpx;
    color: rgba(255, 255, 255, 0.6);
    text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
  }
}
</style>
