# BUX微信小程序 - 自定义TabBar实现

## 项目概述

本项目基于uniapp + Vue 3开发，实现了一个具有胶囊形状悬浮TabBar的微信小程序。

## 功能特性

### 1. 自定义TabBar组件
- 胶囊形状的悬浮设计
- 只在首页显示
- 支持三个功能项：首页、案例、我的
- 使用提供的PNG图片实现选中和非选中状态

### 2. 页面结构

#### 首页 (`/pages/index/index`)
- 背景展示：使用静态图片作为背景（原output.ts为二进制文件）
- 渐变遮罩效果
- 功能卡片导航
- 自定义TabBar（仅在首页显示）

#### 案例页面 (`/pages/case/case`)
- 卡片列表形式展示案例内容
- 响应式设计
- 案例详情查看功能
- 自定义TabBar

#### 我的页面 (`/pages/profile/profile`)
- 用户头像展示和编辑
- 登录/退出功能
- 联系我们信息
- 设备调试入口
- 自定义TabBar

#### 设备调试页面 (`/pages/debug/debug`)
- 设备扫描和连接
- 连接状态显示
- 调试工具集合
- 实时日志输出

## 技术实现

### 组件架构
```
src/
├── components/
│   └── CustomTabBar.vue     # 自定义TabBar组件
├── pages/
│   ├── index/index.vue      # 首页
│   ├── case/case.vue        # 案例页面
│   ├── profile/profile.vue  # 我的页面
│   └── debug/debug.vue      # 设备调试页面
└── static/                  # 静态资源
    ├── home.png            # 首页图标
    ├── home_select.png     # 首页选中图标
    ├── case.png            # 案例图标
    ├── case_select.png     # 案例选中图标
    ├── my.png              # 我的图标
    └── my_select.png       # 我的选中图标
```

### 样式特性
- 使用SCSS预处理器
- 响应式设计适配不同屏幕
- 毛玻璃效果和渐变背景
- 流畅的动画过渡效果

### 交互逻辑
- TabBar只在首页显示
- 页面间导航使用uni.navigateTo
- 首页使用uni.reLaunch确保正确返回
- 设备调试功能模拟真实调试场景

## 使用说明

1. 首页展示主要功能入口
2. 点击TabBar或功能卡片可导航到对应页面
3. 案例页面展示项目案例，支持查看详情
4. 我的页面提供用户管理和设置功能
5. 设备调试页面提供完整的调试工具

## 注意事项

- TabBar采用胶囊悬浮设计，只在首页显示
- 背景视频功能因output.ts为二进制文件，改用静态图片实现
- 所有页面都支持返回导航
- 设备调试功能为模拟实现，可根据实际需求扩展

## 开发环境

- uniapp
- Vue 3
- TypeScript
- SCSS
- 微信小程序
