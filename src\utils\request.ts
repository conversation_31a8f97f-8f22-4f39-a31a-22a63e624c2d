/**
 * 网络请求工具
 */

// 请求配置接口
interface RequestConfig {
  url: string
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
  data?: any
  header?: Record<string, string>
  timeout?: number
}

// 响应数据接口
interface ResponseData<T = any> {
  code: number
  data: T
  message: string
}

// 基础配置
const BASE_URL = 'https://api.example.com'
const TIMEOUT = 10000

/**
 * 请求拦截器
 */
function requestInterceptor(config: RequestConfig): RequestConfig {
  // 添加基础URL
  if (!config.url.startsWith('http')) {
    config.url = BASE_URL + config.url
  }

  // 添加默认header
  config.header = {
    'Content-Type': 'application/json',
    ...config.header
  }

  // 添加token（如果存在）
  const token = uni.getStorageSync('token')
  if (token) {
    config.header.Authorization = `Bearer ${token}`
  }

  console.log('Request:', config)
  return config
}

/**
 * 响应拦截器
 */
function responseInterceptor<T>(response: UniApp.RequestSuccessCallbackResult): Promise<T> {
  console.log('Response:', response)

  const { statusCode, data } = response

  // HTTP状态码检查
  if (statusCode !== 200) {
    return Promise.reject(new Error(`HTTP Error: ${statusCode}`))
  }

  const result = data as ResponseData<T>

  // 业务状态码检查
  if (result.code !== 0) {
    uni.showToast({
      title: result.message || '请求失败',
      icon: 'none'
    })
    return Promise.reject(new Error(result.message || '请求失败'))
  }

  return Promise.resolve(result.data)
}

/**
 * 通用请求方法
 */
function request<T = any>(config: RequestConfig): Promise<T> {
  return new Promise((resolve, reject) => {
    // 请求拦截
    const finalConfig = requestInterceptor(config)

    uni.request({
      url: finalConfig.url,
      method: finalConfig.method || 'GET',
      data: finalConfig.data,
      header: finalConfig.header,
      timeout: finalConfig.timeout || TIMEOUT,
      success: (response) => {
        responseInterceptor<T>(response)
          .then(resolve)
          .catch(reject)
      },
      fail: (error) => {
        console.error('Request failed:', error)
        uni.showToast({
          title: '网络请求失败',
          icon: 'none'
        })
        reject(error)
      }
    })
  })
}

/**
 * GET请求
 */
export function get<T = any>(url: string, data?: any, header?: Record<string, string>): Promise<T> {
  return request<T>({
    url,
    method: 'GET',
    data,
    header
  })
}

/**
 * POST请求
 */
export function post<T = any>(url: string, data?: any, header?: Record<string, string>): Promise<T> {
  return request<T>({
    url,
    method: 'POST',
    data,
    header
  })
}

/**
 * PUT请求
 */
export function put<T = any>(url: string, data?: any, header?: Record<string, string>): Promise<T> {
  return request<T>({
    url,
    method: 'PUT',
    data,
    header
  })
}

/**
 * DELETE请求
 */
export function del<T = any>(url: string, data?: any, header?: Record<string, string>): Promise<T> {
  return request<T>({
    url,
    method: 'DELETE',
    data,
    header
  })
}

// 默认导出
export default {
  get,
  post,
  put,
  delete: del
}
