/**
 * 加载状态管理组合式函数
 */
import { ref } from 'vue'

export function useLoading(initialState = false) {
  const loading = ref(initialState)

  const setLoading = (state: boolean) => {
    loading.value = state
  }

  const startLoading = () => {
    loading.value = true
  }

  const stopLoading = () => {
    loading.value = false
  }

  /**
   * 包装异步函数，自动管理加载状态
   */
  const withLoading = async <T>(asyncFn: () => Promise<T>): Promise<T> => {
    try {
      startLoading()
      const result = await asyncFn()
      return result
    } finally {
      stopLoading()
    }
  }

  return {
    loading,
    setLoading,
    startLoading,
    stopLoading,
    withLoading
  }
}
